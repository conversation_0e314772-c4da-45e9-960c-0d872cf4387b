#!/usr/bin/env node

/**
 * 高级构建分析工具
 * 提供更详细的构建产物分析功能
 */

const fs = require('fs')
const path = require('path')
const chalk = require('chalk')

/**
 * 分析构建产物
 * @param {string} distPath 构建目录路径
 * @param {Object} options 分析选项
 */
async function analyzeBuild(distPath, options = {}) {
  const {
    format = 'console', // 输出格式: console, json, html
    output = null, // 输出文件路径
    detailed = true, // 是否显示详细信息
    threshold = 100 * 1024, // 大文件阈值 (100KB)
    platform = 'unknown'
  } = options

  if (!fs.existsSync(distPath)) {
    throw new Error(`构建目录不存在: ${distPath}`)
  }

  const analysis = await performAnalysis(distPath, { threshold, detailed })

  switch (format) {
    case 'json':
      return outputJSON(analysis, output)
    case 'html':
      return outputHTML(analysis, output)
    case 'console':
    default:
      return outputConsole(analysis, platform)
  }
}

/**
 * 执行构建分析
 */
async function performAnalysis(distPath, options) {
  const { threshold, detailed } = options

  const analysis = {
    timestamp: new Date().toISOString(),
    distPath,
    totalSize: 0,
    totalFiles: 0,
    totalDirs: 0,
    fileTypes: {},
    largestFiles: [],
    directories: {},
    duplicateFiles: [],
    compressionRatio: null
  }

  await analyzeDirectory(distPath, distPath, analysis, { threshold, detailed })

  // 计算压缩比（如果有压缩文件）
  analysis.compressionRatio = calculateCompressionRatio(analysis)

  // 查找重复文件
  if (detailed) {
    analysis.duplicateFiles = findDuplicateFiles(analysis)
  }

  return analysis
}

/**
 * 递归分析目录
 */
async function analyzeDirectory(currentPath, basePath, analysis, options) {
  const { threshold } = options

  try {
    const items = await fs.promises.readdir(currentPath)

    for (const item of items) {
      const itemPath = path.join(currentPath, item)
      const relativePath = path.relative(basePath, itemPath)
      const stats = await fs.promises.stat(itemPath)

      if (stats.isDirectory()) {
        analysis.totalDirs++

        // 记录目录信息
        const dirSize = await calculateDirSize(itemPath)
        analysis.directories[relativePath] = {
          size: dirSize,
          files: 0,
          subdirs: 0
        }

        await analyzeDirectory(itemPath, basePath, analysis, options)
      } else {
        analysis.totalFiles++
        analysis.totalSize += stats.size

        // 统计文件类型
        const ext = path.extname(item).toLowerCase() || '无扩展名'
        if (!analysis.fileTypes[ext]) {
          analysis.fileTypes[ext] = { count: 0, size: 0 }
        }
        analysis.fileTypes[ext].count++
        analysis.fileTypes[ext].size += stats.size

        // 记录大文件
        if (stats.size > threshold) {
          analysis.largestFiles.push({
            path: relativePath,
            size: stats.size,
            formattedSize: formatSize(stats.size),
            type: ext,
            modified: stats.mtime
          })
        }
      }
    }
  } catch (error) {
    console.warn(`无法分析目录 ${currentPath}:`, error.message)
  }
}

/**
 * 计算目录大小
 */
async function calculateDirSize(dirPath) {
  let totalSize = 0

  try {
    const items = await fs.promises.readdir(dirPath)

    for (const item of items) {
      const itemPath = path.join(dirPath, item)
      const stats = await fs.promises.stat(itemPath)

      if (stats.isDirectory()) {
        totalSize += await calculateDirSize(itemPath)
      } else {
        totalSize += stats.size
      }
    }
  } catch (error) {
    // 忽略错误
  }

  return totalSize
}

/**
 * 格式化文件大小
 */
function formatSize(bytes) {
  if (bytes === 0) return '0 B'

  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

/**
 * 计算压缩比
 */
function calculateCompressionRatio(analysis) {
  const compressedExts = ['.gz', '.br', '.zip']
  let compressedSize = 0
  let originalSize = 0

  for (const [ext, info] of Object.entries(analysis.fileTypes)) {
    if (compressedExts.includes(ext)) {
      compressedSize += info.size
    } else {
      originalSize += info.size
    }
  }

  if (compressedSize > 0 && originalSize > 0) {
    return (((originalSize - compressedSize) / originalSize) * 100).toFixed(2)
  }

  return null
}

/**
 * 查找重复文件（基于大小）
 */
function findDuplicateFiles(analysis) {
  const sizeMap = {}

  analysis.largestFiles.forEach((file) => {
    if (!sizeMap[file.size]) {
      sizeMap[file.size] = []
    }
    sizeMap[file.size].push(file)
  })

  return Object.values(sizeMap).filter((files) => files.length > 1)
}

/**
 * 控制台输出
 */
function outputConsole(analysis, platform) {
  console.log('\n' + chalk.cyan('📊 详细构建分析报告'))
  console.log(chalk.gray('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━'))

  // 基本信息
  console.log(chalk.green('📁 构建目录:'), chalk.white(analysis.distPath))
  console.log(chalk.green('🎯 构建平台:'), chalk.white(platform))
  console.log(chalk.green('📦 总体积:'), chalk.bold.yellow(formatSize(analysis.totalSize)))
  console.log(chalk.green('📄 文件数量:'), chalk.white(analysis.totalFiles))
  console.log(chalk.green('📂 目录数量:'), chalk.white(analysis.totalDirs))
  console.log(chalk.green('🕐 分析时间:'), chalk.white(new Date(analysis.timestamp).toLocaleString()))

  // 文件类型分布
  if (Object.keys(analysis.fileTypes).length > 0) {
    console.log('\n' + chalk.blue('📋 文件类型分布:'))
    Object.entries(analysis.fileTypes)
      .sort(([, a], [, b]) => b.size - a.size)
      .slice(0, 10)
      .forEach(([ext, info]) => {
        const percentage = ((info.size / analysis.totalSize) * 100).toFixed(1)
        console.log(
          `  ${chalk.gray('•')} ${chalk.cyan(ext.padEnd(12))}: ${chalk.white(info.count.toString().padEnd(4))} 个 | ${chalk.yellow(formatSize(info.size).padEnd(8))} (${percentage}%)`
        )
      })
  }

  // 目录大小分布
  const sortedDirs = Object.entries(analysis.directories)
    .sort(([, a], [, b]) => b.size - a.size)
    .slice(0, 5)

  if (sortedDirs.length > 0) {
    console.log('\n' + chalk.blue('📂 目录大小分布:'))
    sortedDirs.forEach(([dir, info]) => {
      const percentage = ((info.size / analysis.totalSize) * 100).toFixed(1)
      console.log(`  ${chalk.gray('•')} ${chalk.white(dir.padEnd(20))}: ${chalk.yellow(formatSize(info.size).padEnd(8))} (${percentage}%)`)
    })
  }

  // 大文件列表
  if (analysis.largestFiles.length > 0) {
    console.log('\n' + chalk.blue('📈 大文件列表:'))
    analysis.largestFiles
      .sort((a, b) => b.size - a.size)
      .slice(0, 8)
      .forEach((file) => {
        console.log(`  ${chalk.gray('•')} ${chalk.white(file.formattedSize.padEnd(8))} ${chalk.gray(file.path)}`)
      })
  }

  // 压缩信息
  if (analysis.compressionRatio) {
    console.log('\n' + chalk.blue('🗜️  压缩信息:'))
    console.log(`  ${chalk.gray('•')} 压缩比: ${chalk.green(analysis.compressionRatio + '%')}`)
  }

  // 重复文件
  if (analysis.duplicateFiles.length > 0) {
    console.log('\n' + chalk.blue('🔄 可能的重复文件:'))
    analysis.duplicateFiles.slice(0, 3).forEach((files) => {
      console.log(`  ${chalk.gray('•')} ${chalk.yellow(formatSize(files[0].size))}: ${files.length} 个文件`)
      files.forEach((file) => {
        console.log(`    ${chalk.gray('-')} ${chalk.white(file.path)}`)
      })
    })
  }

  // 优化建议
  console.log('\n' + chalk.magenta('💡 优化建议:'))
  const suggestions = generateSuggestions(analysis)
  suggestions.forEach((suggestion) => {
    console.log(`  ${chalk.gray('•')} ${chalk.yellow(suggestion)}`)
  })

  console.log(chalk.gray('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━'))
  console.log(chalk.green('✅ 构建分析完成\n'))
}

/**
 * 生成优化建议
 */
function generateSuggestions(analysis) {
  const suggestions = []

  if (analysis.totalSize > 5 * 1024 * 1024) {
    suggestions.push('包体积较大 (>5MB)，建议检查是否有未使用的依赖')
  }

  if (analysis.largestFiles.length > 5) {
    suggestions.push('存在多个大文件，建议进行代码分割或资源优化')
  }

  const jsFiles = analysis.fileTypes['.js']
  if (jsFiles && jsFiles.count > 20) {
    suggestions.push('JS 文件较多，建议启用代码压缩和合并')
  }

  const imageFiles = ['.png', '.jpg', '.jpeg', '.gif', '.svg'].reduce((total, ext) => {
    return total + (analysis.fileTypes[ext]?.size || 0)
  }, 0)

  if (imageFiles > 1024 * 1024) {
    suggestions.push('图片资源较大，建议使用 WebP 格式或压缩图片')
  }

  if (analysis.duplicateFiles.length > 0) {
    suggestions.push('发现可能的重复文件，建议检查并合并')
  }

  if (suggestions.length === 0) {
    suggestions.push('构建产物看起来很健康！')
  }

  return suggestions
}

/**
 * JSON 输出
 */
function outputJSON(analysis, outputPath) {
  const jsonData = JSON.stringify(analysis, null, 2)

  if (outputPath) {
    fs.writeFileSync(outputPath, jsonData)
    console.log(chalk.green(`✅ 分析结果已保存到: ${outputPath}`))
  } else {
    console.log(jsonData)
  }

  return analysis
}

/**
 * HTML 输出
 */
function outputHTML(analysis, outputPath) {
  // 这里可以生成 HTML 报告
  console.log(chalk.yellow('HTML 输出功能待实现'))
  return analysis
}

// 命令行接口
if (require.main === module) {
  const args = process.argv.slice(2)
  const distPath = args[0] || path.resolve(__dirname, '../dist')

  analyzeBuild(distPath, {
    format: 'console',
    detailed: true,
    platform: process.env.TARO_ENV || 'unknown'
  }).catch((error) => {
    console.error(chalk.red('❌ 分析失败:'), error.message)
    process.exit(1)
  })
}

module.exports = {
  analyzeBuild,
  formatSize,
  calculateDirSize
}
