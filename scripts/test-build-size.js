#!/usr/bin/env node

/**
 * 测试构建体积统计功能
 * 用于验证插件是否正常工作
 */

const { outputBuildSizeReport, calculateDirSize, formatSize } = require('./build-size-plugin')
const path = require('path')

async function testBuildSizeReport() {
  console.log('🧪 测试构建体积统计功能...\n')

  const distPath = path.resolve(__dirname, '../dist')

  try {
    // 测试基本功能
    console.log('1. 测试基本体积计算...')
    const size = await calculateDirSize(distPath)
    console.log(`   dist 目录大小: ${formatSize(size)}\n`)

    // 测试完整报告
    console.log('2. 测试完整体积报告...')
    await outputBuildSizeReport(distPath, 'test')

    console.log('✅ 测试完成！')
  } catch (error) {
    console.error('❌ 测试失败:', error.message)
    process.exit(1)
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  testBuildSizeReport()
}

module.exports = { testBuildSizeReport }
