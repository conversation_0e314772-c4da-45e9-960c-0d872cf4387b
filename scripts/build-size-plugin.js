/**
 * Taro 构建体积统计插件
 * 在构建完成后自动输出 dist 文件夹的体积信息
 */

const fs = require('fs')
const path = require('path')
const chalk = require('chalk')

/**
 * 计算文件夹大小
 * @param {string} dirPath 文件夹路径
 * @returns {Promise<number>} 文件夹大小（字节）
 */
async function calculateDirSize(dirPath) {
  let totalSize = 0

  try {
    const items = await fs.promises.readdir(dirPath)

    for (const item of items) {
      const itemPath = path.join(dirPath, item)
      const stats = await fs.promises.stat(itemPath)

      if (stats.isDirectory()) {
        totalSize += await calculateDirSize(itemPath)
      } else {
        totalSize += stats.size
      }
    }
  } catch (error) {
    console.warn(`无法读取目录 ${dirPath}:`, error.message)
  }

  return totalSize
}

/**
 * 格式化文件大小
 * @param {number} bytes 字节数
 * @returns {string} 格式化后的大小
 */
function formatSize(bytes) {
  if (bytes === 0) return '0 B'

  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

/**
 * 获取文件详细信息
 * @param {string} dirPath 目录路径
 * @returns {Promise<Object>} 文件统计信息
 */
async function getFileStats(dirPath) {
  const stats = {
    totalFiles: 0,
    totalDirs: 0,
    fileTypes: {},
    largestFiles: []
  }

  async function traverse(currentPath) {
    try {
      const items = await fs.promises.readdir(currentPath)

      for (const item of items) {
        const itemPath = path.join(currentPath, item)
        const itemStats = await fs.promises.stat(itemPath)

        if (itemStats.isDirectory()) {
          stats.totalDirs++
          await traverse(itemPath)
        } else {
          stats.totalFiles++

          // 统计文件类型
          const ext = path.extname(item).toLowerCase() || '无扩展名'
          stats.fileTypes[ext] = (stats.fileTypes[ext] || 0) + 1

          // 记录大文件（超过 100KB）
          if (itemStats.size > 100 * 1024) {
            stats.largestFiles.push({
              path: path.relative(dirPath, itemPath),
              size: itemStats.size,
              formattedSize: formatSize(itemStats.size)
            })
          }
        }
      }
    } catch (error) {
      console.warn(`无法遍历目录 ${currentPath}:`, error.message)
    }
  }

  await traverse(dirPath)

  // 按大小排序大文件列表
  stats.largestFiles.sort((a, b) => b.size - a.size)
  stats.largestFiles = stats.largestFiles.slice(0, 10) // 只保留前10个最大的文件

  return stats
}

/**
 * 输出构建体积报告
 * @param {string} distPath dist 目录路径
 * @param {string} platform 构建平台
 */
async function outputBuildSizeReport(distPath, platform = 'unknown') {
  if (!fs.existsSync(distPath)) {
    console.warn(chalk.yellow(`⚠️  构建目录不存在: ${distPath}`))
    return
  }

  console.log('\n' + chalk.cyan('📊 构建体积统计报告'))
  console.log(chalk.gray('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━'))

  try {
    // 计算总体积
    const totalSize = await calculateDirSize(distPath)
    const fileStats = await getFileStats(distPath)

    // 基本信息
    console.log(chalk.green('📁 构建目录:'), chalk.white(distPath))
    console.log(chalk.green('🎯 构建平台:'), chalk.white(platform))
    console.log(chalk.green('📦 总体积:'), chalk.bold.yellow(formatSize(totalSize)))
    console.log(chalk.green('📄 文件数量:'), chalk.white(fileStats.totalFiles))
    console.log(chalk.green('📂 目录数量:'), chalk.white(fileStats.totalDirs))

    // 文件类型统计
    if (Object.keys(fileStats.fileTypes).length > 0) {
      console.log('\n' + chalk.blue('📋 文件类型分布:'))
      Object.entries(fileStats.fileTypes)
        .sort(([, a], [, b]) => b - a)
        .slice(0, 8)
        .forEach(([ext, count]) => {
          console.log(`  ${chalk.gray('•')} ${chalk.cyan(ext.padEnd(8))}: ${chalk.white(count)} 个`)
        })
    }

    // 大文件列表
    if (fileStats.largestFiles.length > 0) {
      console.log('\n' + chalk.blue('📈 较大文件 (>100KB):'))
      fileStats.largestFiles.slice(0, 5).forEach((file) => {
        console.log(`  ${chalk.gray('•')} ${chalk.white(file.formattedSize.padEnd(8))} ${chalk.gray(file.path)}`)
      })
    }

    // 性能建议
    console.log('\n' + chalk.magenta('💡 优化建议:'))
    if (totalSize > 2 * 1024 * 1024) {
      // 大于 2MB
      console.log(`  ${chalk.gray('•')} ${chalk.yellow('包体积较大，建议检查是否有未使用的依赖')}`)
    }
    if (fileStats.largestFiles.length > 3) {
      console.log(`  ${chalk.gray('•')} ${chalk.yellow('存在多个大文件，建议进行代码分割或资源优化')}`)
    }
    if (fileStats.fileTypes['.js'] > 20) {
      console.log(`  ${chalk.gray('•')} ${chalk.yellow('JS 文件较多，建议启用代码压缩和合并')}`)
    }

    console.log(chalk.gray('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━'))
    console.log(chalk.green('✅ 构建体积统计完成\n'))
  } catch (error) {
    console.error(chalk.red('❌ 计算构建体积时出错:'), error.message)
  }
}

/**
 * Taro 构建体积统计插件
 */
function buildSizePlugin(ctx, options = {}) {
  const {
    enabled = true,
    showDetails = true,
    threshold = 0 // 体积阈值，超过此值才显示警告（字节）
  } = options

  if (!enabled) return

  // 监听构建完成事件
  ctx.onBuildComplete(async () => {
    const { outputRoot = 'dist' } = ctx.initialConfig
    const distPath = path.resolve(ctx.paths.appPath, outputRoot)
    const platform = process.env.TARO_ENV || 'unknown'

    await outputBuildSizeReport(distPath, platform)
  })

  // 监听构建结束事件（每次保存都会触发）
  ctx.onBuildFinish(async ({ error, stats, isWatch }) => {
    if (error || isWatch) return // 只在非 watch 模式且无错误时执行

    const { outputRoot = 'dist' } = ctx.initialConfig
    const distPath = path.resolve(ctx.paths.appPath, outputRoot)

    if (threshold > 0) {
      const totalSize = await calculateDirSize(distPath)
      if (totalSize > threshold) {
        console.log(chalk.yellow(`⚠️  构建体积 ${formatSize(totalSize)} 超过阈值 ${formatSize(threshold)}`))
      }
    }
  })
}

// CommonJS 导出
module.exports = buildSizePlugin
module.exports.calculateDirSize = calculateDirSize
module.exports.formatSize = formatSize
module.exports.outputBuildSizeReport = outputBuildSizeReport

// ES6 默认导出
module.exports.default = buildSizePlugin
