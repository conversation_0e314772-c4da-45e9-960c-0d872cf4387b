{"name": "ai-mp-wode-shop", "version": "1.0.0", "private": true, "description": "ARTIN · 爱定制", "templateInfo": {"name": "default", "typescript": true, "css": "sass", "framework": "React"}, "scripts": {"postinstall": "weapp-tw patch", "prepare": "husky", "new": "taro new", "build:test:weapp": "taro build --type weapp --mode test && rm -rf dist/static", "build:live:weapp": "taro build --type weapp --mode live && rm -rf dist/static", "build:swan": "taro build --type swan", "build:alipay": "taro build --type alipay", "build:tt": "taro build --type tt", "build:h5": "taro build --type h5", "build:rn": "taro build --type rn", "build:qq": "taro build --type qq", "build:jd": "taro build --type jd", "build:harmony-hybrid": "taro build --type harmony-hybrid", "dev:weapp": "taro build --type weapp --watch --mode local", "dev:swan": "npm run build:swan -- --watch", "dev:alipay": "npm run build:alipay -- --watch", "dev:tt": "npm run build:tt -- --watch", "dev:h5": "taro build --type h5 --watch --mode local", "dev:rn": "npm run build:rn -- --watch", "dev:qq": "npm run build:qq -- --watch", "dev:jd": "npm run build:jd -- --watch", "dev:harmony-hybrid": "npm run build:harmony-hybrid -- --watch", "size": "node scripts/test-build-size.js", "size:analyze": "npm run build:weapp && npm run size", "analyze": "node scripts/build-analyzer.js", "analyze:json": "node scripts/build-analyzer.js dist --format=json --output=build-analysis.json", "build:analyze": "npm run build:weapp && npm run analyze", "lint": "eslint", "lint:fix": "eslint --fix", "prettier": "npx prettier --write ."}, "browserslist": ["defaults and fully supports es6-module", "maintained node versions"], "author": "", "dependencies": {"@babel/runtime": "^7.24.4", "@esbuild/darwin-arm64": "^0.25.6", "@swc/core": "^1.12.9", "@tarojs/binding-darwin-arm64": "^4.1.3", "@tarojs/components": "4.1.3", "@tarojs/helper": "4.1.3", "@tarojs/plugin-framework-react": "4.1.3", "@tarojs/plugin-platform-alipay": "4.1.3", "@tarojs/plugin-platform-h5": "4.1.3", "@tarojs/plugin-platform-harmony-hybrid": "4.1.3", "@tarojs/plugin-platform-qq": "4.1.3", "@tarojs/plugin-platform-swan": "4.1.3", "@tarojs/plugin-platform-tt": "4.1.3", "@tarojs/plugin-platform-weapp": "4.1.3", "@tarojs/react": "4.1.3", "@tarojs/runtime": "4.1.3", "@tarojs/shared": "4.1.3", "@tarojs/taro": "4.1.3", "jotai": "^2.12.5", "react": "^18.0.0", "react-dom": "^18.0.0", "react-markdown": "^10.1.0", "react-use": "^17.6.0", "react-vant": "^3.3.5", "remark-breaks": "^4.0.0"}, "devDependencies": {"@babel/core": "^7.24.4", "@babel/plugin-transform-class-properties": "7.25.9", "@babel/preset-react": "^7.24.1", "@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@pmmmwh/react-refresh-webpack-plugin": "^0.5.5", "@tarojs/cli": "4.1.3", "@tarojs/plugin-generator": "4.1.3", "@tarojs/plugin-html": "4.1.3", "@tarojs/taro-loader": "4.1.3", "@tarojs/webpack5-runner": "4.1.3", "@types/node": "^20", "@types/react": "^18.0.0", "@types/webpack-env": "^1.13.6", "autoprefixer": "^10.4.21", "babel-preset-taro": "4.1.3", "eslint": "^8.57.0", "eslint-config-taro": "4.1.3", "eslint-plugin-react": "^7.34.1", "eslint-plugin-react-hooks": "^4.4.0", "husky": "^9.1.7", "lint-staged": "^16.1.2", "postcss": "^8.5.6", "prettier": "^3.6.2", "react-refresh": "^0.14.0", "sass": "^1.89.2", "stylelint": "^16.4.0", "stylelint-config-standard": "^38.0.0", "tailwindcss": "^3.4.0", "tsconfig-paths-webpack-plugin": "^4.1.0", "typescript": "^5.4.5", "weapp-tailwindcss": "^4.1.10", "webpack": "5.91.0", "webpack-aliyun-oss": "^0.3.13"}}