/*
 * @Author: 宋璟阳 <EMAIL>
 * @Date: 2025-07-02 10:48:59
 * @LastEditors: 宋璟阳 <EMAIL>
 * @LastEditTime: 2025-07-02 10:49:09
 * @FilePath: /wechat-miniprogram/postcss.config.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
// postcss 插件以 object 方式注册的话，是按照由上到下的顺序执行的
module.exports = {
  plugins: {
    tailwindcss: {},
    autoprefixer: {}
  }
}
