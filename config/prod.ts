import type { UserConfigExport } from '@tarojs/cli'
import WebpackAliyunOss from 'webpack-aliyun-oss'

const distPath = `/ai-mp-wode-shop/${process.env.TARO_ENV}/${process.env.TARO_APP_ENV}`

export default {
  mini: {
    webpackChain(chain) {
      chain.plugin('webpack-aliyun-oss').use(WebpackAliyunOss, [
        {
          from: ['./dist/static/**'], //排除html文件
          dist: distPath,
          region: 'oss-cn-hangzhou',
          accessKeyId: 'LTAI4GDaQcWnC9KqLgvjKwih',
          accessKeySecret: '******************************',
          bucket: 'msb-xiaoxiong-fe'
        }
      ])
    }
  },
  h5: {
    /**
     * WebpackChain 插件配置
     * @docs https://github.com/neutrinojs/webpack-chain
     */
    // webpackChain (chain) {
    //   /**
    //    * 如果 h5 端编译后体积过大，可以使用 webpack-bundle-analyzer 插件对打包体积进行分析。
    //    * @docs https://github.com/webpack-contrib/webpack-bundle-analyzer
    //    */
    //   chain.plugin('analyzer')
    //     .use(require('webpack-bundle-analyzer').BundleAnalyzerPlugin, [])
    //   /**
    //    * 如果 h5 端首屏加载时间过长，可以使用 prerender-spa-plugin 插件预加载首页。
    //    * @docs https://github.com/chrisvfritz/prerender-spa-plugin
    //    */
    //   const path = require('path')
    //   const Prerender = require('prerender-spa-plugin')
    //   const staticDir = path.join(__dirname, '..', 'dist')
    //   chain
    //     .plugin('prerender')
    //     .use(new Prerender({
    //       staticDir,
    //       routes: [ '/pages/index/index' ],
    //       postProcess: (context) => ({ ...context, outputPath: path.join(staticDir, 'index.html') })
    //     }))
    // }
  }
} satisfies UserConfigExport<'webpack5'>
