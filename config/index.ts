import path from 'path'
import { defineConfig, type UserConfigExport } from '@tarojs/cli'
import TsconfigPathsPlugin from 'tsconfig-paths-webpack-plugin'
import { UnifiedWebpackPluginV5 } from 'weapp-tailwindcss/webpack'

import devConfig from './dev'
import prodConfig from './prod'

console.log('NODE_ENV:', process.env.NODE_ENV)
console.log('TARO_ENV:', process.env.TARO_ENV)
console.log('TARO_APP_ENV:', process.env.TARO_APP_ENV)

const ossPath = 'https://fe-cdn.xiaoxiongmeishu.com'
let projectPublicPath = '/'

if (process.env.TARO_APP_ENV !== 'local') {
  projectPublicPath = `${ossPath}/ai-mp-wode-shop/${process.env.TARO_ENV}/${process.env.TARO_APP_ENV}/`
}

// https://taro-docs.jd.com/docs/next/config#defineconfig-辅助函数
export default defineConfig<'webpack5'>(async (merge) => {
  const baseConfig: UserConfigExport<'webpack5'> = {
    projectName: 'ai-mp-wode-shop',
    date: '2025-7-2',
    designWidth: 750,
    deviceRatio: {
      640: 2.34 / 2,
      750: 1,
      375: 2,
      828: 1.81 / 2
    },
    sourceRoot: 'src',
    outputRoot: 'dist',
    alias: {
      '@': path.resolve(__dirname, '..', 'src')
    },
    plugins: [
      '@tarojs/plugin-html',
      '@tarojs/plugin-generator'
      // 构建体积统计插件
      // [
      //   require.resolve('../scripts/build-size-plugin'),
      //   {
      //     enabled: true, // 是否启用插件
      //     showDetails: true, // 是否显示详细信息
      //     threshold: 0 // 体积阈值（字节），超过此值显示警告
      //   }
      // ]
    ],
    defineConstants: {},
    copy: {
      patterns: [],
      options: {}
    },
    framework: 'react',
    compiler: {
      type: 'webpack5',
      // 仅 webpack5 支持依赖预编译配置
      prebundle: {
        enable: false // 暂时禁用prebundle解决vendors.js问题
      }
    },
    cache: {
      enable: true, // 开启 Webpack 持久化缓存，提升二次编译速度
      buildDependencies: {
        // 当这些文件发生变化时，缓存将失效
        config: [__filename]
      }
    },
    mini: {
      postcss: {
        pxtransform: {
          enable: true,
          config: {}
        },
        cssModules: {
          enable: false, // 默认为 false，如需使用 css modules 功能，则设为 true
          config: {
            namingPattern: 'module', // 转换模式，取值为 global/module
            generateScopedName: '[name]__[local]___[hash:base64:5]'
          }
        }
      },
      webpackChain(chain) {
        chain.resolve.plugin('tsconfig-paths').use(TsconfigPathsPlugin)

        // 修复vendors.js问题 - 禁用代码分包
        chain.optimization.splitChunks({
          chunks: 'all',
          cacheGroups: {
            default: false,
            vendors: false,
            // 将所有代码打包到主包中
            common: {
              name: 'common',
              minChunks: 1,
              chunks: 'all',
              enforce: true
            }
          }
        })

        chain.merge({
          plugin: {
            install: {
              plugin: UnifiedWebpackPluginV5,
              args: [
                {
                  appType: 'taro',
                  // 下面个配置，会开启 rem -> rpx 的转化
                  rem2rpx: true
                }
              ]
            }
          }
        })
      },
      optimizeMainPackage: {
        enable: true
      },
      // 配置图片处理，降低 base64 转换阈值以避免性能问题
      imageUrlLoaderOption: {
        limit: 1, // 2KB，默认是 10KB，降低阈值避免大图片转 base64
        name: 'static/[name].[hash][ext]',
        publicPath: projectPublicPath
      }
    },
    h5: {
      publicPath: '/',
      staticDirectory: 'static',
      output: {
        filename: 'js/[name].[hash:8].js',
        chunkFilename: 'js/[name].[chunkhash:8].js'
      },
      miniCssExtractPluginOption: {
        ignoreOrder: true,
        filename: 'css/[name].[hash].css',
        chunkFilename: 'css/[name].[chunkhash].css'
      },
      postcss: {
        autoprefixer: {
          enable: true,
          config: {}
        },
        cssModules: {
          enable: false, // 默认为 false，如需使用 css modules 功能，则设为 true
          config: {
            namingPattern: 'module', // 转换模式，取值为 global/module
            generateScopedName: '[name]__[local]___[hash:base64:5]'
          }
        }
      },
      webpackChain(chain) {
        chain.resolve.plugin('tsconfig-paths').use(TsconfigPathsPlugin)
      }
    },
    rn: {
      appName: 'taroDemo',
      postcss: {
        cssModules: {
          enable: false // 默认为 false，如需使用 css modules 功能，则设为 true
        }
      }
    }
  }

  if (process.env.TARO_APP_ENV === 'local') {
    // 本地开发构建配置（不混淆压缩）
    return merge({}, baseConfig, devConfig)
  }
  // 生产构建配置（默认开启压缩混淆等）
  return merge({}, baseConfig, prodConfig)
})
