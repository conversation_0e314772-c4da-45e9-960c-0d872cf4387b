# 微信小程序项目

> 基于 Taro + React + TypeScript + TailwindCSS + Zustand 的现代化微信小程序开发模板

## ✨ 项目特性

- 🚀 **Taro 4.1.3** - 支持多端开发，一套代码多端运行
- ⚛️ **React 18** - 使用最新的 React 特性和 Hooks
- 🔷 **TypeScript** - 完整的类型支持，提升开发体验
- 🎨 **TailwindCSS** - 原子化 CSS 框架，快速构建界面
- 🗃️ **Zustand** - 轻量级状态管理库，简单易用
- 🛠️ **完整工具链** - ESLint + Stylelint + Husky + Commitlint
- ⚡ **性能优化** - Webpack 持久化缓存，提升编译速度
- 📊 **构建分析** - 自动构建体积统计，优化建议
- 📱 **多端支持** - 微信小程序、H5、支付宝小程序等

## 🏗️ 技术栈

| 技术        | 版本   | 说明             |
| ----------- | ------ | ---------------- |
| Taro        | 4.1.3  | 多端统一开发框架 |
| React       | 18.0.0 | 前端框架         |
| TypeScript  | 5.4.5  | 类型系统         |
| TailwindCSS | 3.4.0  | 原子化 CSS 框架  |
| Zustand     | 5.0.6  | 状态管理库       |
| Webpack     | 5.91.0 | 构建工具         |

## 📁 项目结构

```
wechat-miniprogram/
├── src/                    # 源码目录
│   ├── pages/             # 页面目录
│   │   ├── index/         # 首页
│   │   └── store-demo/    # 状态管理示例页
│   ├── store/             # 状态管理
│   │   ├── index.ts       # 导出文件
│   │   ├── userStore.ts   # 用户状态
│   │   ├── appStore.ts    # 应用状态
│   │   └── counterStore.ts # 计数器状态
│   ├── app.config.ts      # 应用配置
│   ├── app.css           # 全局样式
│   └── app.ts            # 应用入口
├── config/                # 构建配置
│   ├── index.ts          # 主配置文件
│   ├── dev.ts            # 开发环境配置
│   └── prod.ts           # 生产环境配置
├── dist/                 # 构建输出目录
├── types/                # 类型定义
├── package.json          # 项目配置
├── tsconfig.json         # TypeScript 配置
├── tailwind.config.js    # TailwindCSS 配置
├── postcss.config.js     # PostCSS 配置
└── project.config.json   # 微信小程序配置
```

## 🚀 快速开始

### 环境要求

- Node.js >= 16.0.0
- npm >= 8.0.0
- 微信开发者工具

### 安装依赖

```bash
# 克隆项目
git clone <repository-url>
cd wechat-miniprogram

# 安装依赖
npm install
```

### 开发命令

```bash
# 微信小程序开发
npm run dev:weapp

# H5 开发
npm run dev:h5

# 支付宝小程序开发
npm run dev:alipay

# 其他平台
npm run dev:swan      # 百度小程序
npm run dev:tt        # 字节跳动小程序
npm run dev:qq        # QQ 小程序
npm run dev:jd        # 京东小程序
```

### 构建命令

```bash
# 微信小程序构建
npm run build:weapp

# H5 构建
npm run build:h5

# 其他平台构建
npm run build:alipay  # 支付宝小程序
npm run build:swan    # 百度小程序
npm run build:tt      # 字节跳动小程序

# 构建体积分析
npm run size          # 查看当前构建体积
npm run analyze       # 详细构建分析
npm run build:analyze # 构建并分析
```

## 📖 开发指南

### 页面开发

1. 在 `src/pages/` 目录下创建新页面
2. 每个页面包含：
   - `index.tsx` - 页面组件
   - `index.css` - 页面样式
   - `index.config.ts` - 页面配置

```typescript
// src/pages/example/index.tsx
import { View, Text } from "@tarojs/components";
import { useLoad } from "@tarojs/taro";
import "./index.css";

export default function Example() {
  useLoad(() => {
    console.log("页面加载");
  });

  return (
    <View className="example">
      <Text>示例页面</Text>
    </View>
  );
}
```

### 状态管理 (Zustand)

项目使用 Zustand 进行状态管理，支持 TypeScript 和持久化存储。

```typescript
// 使用状态
import { useUserStore } from "@/store";

function MyComponent() {
  const { user, login, logout } = useUserStore();

  // 选择性订阅
  const userName = useUserStore((state) => state.user?.name);

  return (
    <View>
      {user ? (
        <Text>欢迎，{user.name}！</Text>
      ) : (
        <Button onClick={() => login(userData)}>登录</Button>
      )}
    </View>
  );
}
```

### 样式开发 (TailwindCSS)

项目集成了 TailwindCSS，支持在小程序中使用原子化 CSS。

```tsx
<View className="flex flex-col items-center justify-center min-h-screen bg-blue-50 p-4">
  <Text className="text-2xl font-bold text-blue-600 mb-4">
    Hello TailwindCSS!
  </Text>
  <Button className="bg-purple-500 text-white px-6 py-3 rounded-lg">
    按钮
  </Button>
</View>
```

## ⚙️ 配置说明

### Taro 配置

主要配置文件：`config/index.ts`

```typescript
export default defineConfig({
  projectName: "wechat-miniprogram",
  framework: "react",
  compiler: "webpack5",
  cache: {
    enable: true, // 开启持久化缓存
  },
  // 其他配置...
});
```

### TailwindCSS 配置

配置文件：`tailwind.config.js`

- 已配置小程序适配
- 禁用了 preflight（小程序不需要）
- 支持 TypeScript 文件扫描

### 状态管理配置

项目包含三个预配置的状态管理模块：

#### 用户状态 (`userStore.ts`)

```typescript
interface UserState {
  user: User | null;
  isLoggedIn: boolean;
  loading: boolean;
  setUser: (user: User) => void;
  login: (user: User) => void;
  logout: () => void;
}
```

#### 应用状态 (`appStore.ts`)

```typescript
interface AppState {
  theme: "light" | "dark" | "auto";
  language: "zh-CN" | "en-US";
  isLoading: boolean;
  networkStatus: "online" | "offline";
  setTheme: (theme: Theme) => void;
  initApp: () => Promise<void>;
}
```

#### 计数器状态 (`counterStore.ts`)

```typescript
interface CounterState {
  count: number;
  step: number;
  history: number[];
  increment: () => void;
  decrement: () => void;
  reset: () => void;
  // 计算属性
  isEven: boolean;
  isPositive: boolean;
}
```

### 环境变量配置

创建 `.env` 文件配置环境变量：

```bash
# .env.development
NODE_ENV=development
TARO_APP_ID=your_dev_appid

# .env.production
NODE_ENV=production
TARO_APP_ID=your_prod_appid
```

## 🚀 部署指南

### 微信小程序部署

1. 构建项目：`npm run build:weapp`
2. 使用微信开发者工具打开 `dist` 目录
3. 预览和上传代码

### H5 部署

1. 构建项目：`npm run build:h5`
2. 将 `dist` 目录部署到静态服务器
3. 配置路由（如使用 Nginx）：

```nginx
# nginx.conf
location / {
  try_files $uri $uri/ /index.html;
}
```

### 其他平台部署

```bash
# 支付宝小程序
npm run build:alipay

# 百度小程序
npm run build:swan

# 字节跳动小程序
npm run build:tt

# QQ 小程序
npm run build:qq

# 京东小程序
npm run build:jd
```

## 🛠️ 开发工具链

### 代码质量工具

#### ESLint 配置

```json
{
  "extends": ["taro/react"],
  "rules": {
    "react-hooks/exhaustive-deps": "warn",
    "@typescript-eslint/no-unused-vars": "error"
  }
}
```

#### Stylelint 配置

```javascript
// stylelint.config.mjs
export default {
  extends: "stylelint-config-standard",
  rules: {
    "selector-class-pattern": null,
  },
};
```

#### Husky Git Hooks

```json
{
  "husky": {
    "hooks": {
      "pre-commit": "lint-staged",
      "commit-msg": "commitlint -E HUSKY_GIT_PARAMS"
    }
  }
}
```

### 调试工具

1. **微信开发者工具**：小程序调试
2. **Chrome DevTools**：H5 调试
3. **React DevTools**：组件调试
4. **Zustand DevTools**：状态调试

```typescript
// 开启 Zustand DevTools
const useStore = create(
  devtools(
    (set) => ({
      // store implementation
    }),
    {
      name: "my-store", // DevTools 中显示的名称
    }
  )
);
```

## 📝 开发规范

### 代码规范

- 使用 ESLint 进行代码检查
- 使用 Stylelint 进行样式检查
- 使用 TypeScript 严格模式

### 提交规范

项目使用 Commitlint 规范提交信息：

```bash
feat: 新功能
fix: 修复问题
docs: 文档更新
style: 代码格式调整
refactor: 代码重构
test: 测试相关
chore: 构建过程或辅助工具的变动
```

### 目录规范

- 页面文件使用 kebab-case 命名
- 组件文件使用 PascalCase 命名
- 工具函数使用 camelCase 命名

## 🤝 贡献指南

1. Fork 本仓库
2. 创建特性分支：`git checkout -b feature/new-feature`
3. 提交更改：`git commit -m 'feat: add new feature'`
4. 推送分支：`git push origin feature/new-feature`
5. 提交 Pull Request

## 📄 许可证

[MIT License](LICENSE)

## 💡 最佳实践

### 状态管理最佳实践

1. **状态分离**：按功能模块分离状态，避免单一大状态
2. **选择性订阅**：使用选择器避免不必要的重渲染
3. **持久化存储**：重要状态使用 persist 中间件持久化

```typescript
// 推荐：选择性订阅
const userName = useUserStore((state) => state.user?.name);

// 不推荐：订阅整个状态
const { user } = useUserStore();
```

### TailwindCSS 小程序适配

1. **单位转换**：项目已配置 `weapp-tailwindcss` 自动转换单位
2. **样式隔离**：小程序组件样式自动隔离，无需担心样式冲突
3. **性能优化**：只打包使用到的样式类

```tsx
// 响应式设计
<View className="w-full sm:w-1/2 lg:w-1/3">
  <Text className="text-sm sm:text-base lg:text-lg">响应式文本</Text>
</View>
```

### 性能优化建议

1. **代码分割**：使用动态导入分割代码
2. **图片优化**：使用 WebP 格式，合理设置图片尺寸
3. **缓存策略**：合理使用 Taro 缓存机制
4. **构建分析**：使用内置的构建体积分析工具

```typescript
// 动态导入示例
const LazyComponent = lazy(() => import("./LazyComponent"));
```

### 构建体积分析

项目集成了自动构建体积分析功能：

```bash
# 每次构建后自动显示体积统计
npm run build:weapp

# 手动分析当前构建
npm run analyze

# 导出详细分析报告
npm run analyze:json
```

**分析功能特性：**

- 📊 总体积和文件数量统计
- 📋 文件类型分布分析
- 📈 大文件检测和列表
- 💡 基于分析结果的优化建议
- 📄 支持 JSON 格式导出

详细使用说明请参考：[构建体积分析文档](docs/build-size-analysis.md)

## 🐛 常见问题

### Q: TailwindCSS 样式不生效？

A: 检查以下几点：

1. 确保文件在 `tailwind.config.js` 的 `content` 配置中
2. 运行 `npm run postinstall` 确保 weapp-tw 补丁生效
3. 重启开发服务器

### Q: 状态持久化不工作？

A: 检查以下几点：

1. 确保使用了 `persist` 中间件
2. 检查小程序存储权限
3. 查看控制台是否有错误信息

### Q: 多端兼容性问题？

A: 建议：

1. 使用 Taro 提供的统一 API
2. 通过 `process.env.TARO_ENV` 判断平台
3. 查阅 [Taro 多端兼容文档](https://taro-docs.jd.com/docs/envs)

## 📊 项目统计

- **代码行数**：约 1000+ 行
- **页面数量**：2 个示例页面
- **状态管理**：3 个 Store 模块
- **支持平台**：8+ 个小程序平台 + H5
- **构建时间**：约 10-30 秒（启用缓存后）

## 🔄 更新日志

### v1.0.0 (2025-07-02)

- ✨ 初始化项目结构
- ✨ 集成 Taro 4.1.3 + React 18
- ✨ 配置 TailwindCSS 小程序适配
- ✨ 集成 Zustand 状态管理
- ✨ 配置完整的开发工具链
- ✨ 添加状态管理示例页面
- ✨ 开启 Webpack 持久化缓存优化

## 🔗 相关链接

- [Taro 官方文档](https://taro-docs.jd.com/)
- [React 官方文档](https://react.dev/)
- [TailwindCSS 官方文档](https://tailwindcss.com/)
- [Zustand 官方文档](https://zustand-demo.pmnd.rs/)
- [微信小程序官方文档](https://developers.weixin.qq.com/miniprogram/dev/framework/)
- [weapp-tailwindcss 文档](https://weapp-tw.icebreaker.top/)

## 👥 贡献者

感谢所有为这个项目做出贡献的开发者！

## ⭐ Star History

如果这个项目对你有帮助，请给它一个 ⭐️！
