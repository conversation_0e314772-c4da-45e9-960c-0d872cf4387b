/*
 * @Author: 宋璟阳 <EMAIL>
 * @Date: 2025-07-02 10:49:17
 * @LastEditors: 宋璟阳 <EMAIL>
 * @LastEditTime: 2025-07-02 10:49:23
 * @FilePath: /wechat-miniprogram/tailwind.config.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
/** @type {import('tailwindcss').Config} */
export default {
  // 这里给出了一份 taro 通用示例，具体要根据你自己项目的目录结构进行配置
  // 比如你使用 vue3 项目，你就需要把 vue 这个格式也包括进来
  // 不在 content glob 表达式中包括的文件，在里面编写 tailwindcss class，是不会生成对应的 css 工具类的
  content: ['./public/index.html', './src/**/*.{html,js,ts,jsx,tsx}'],
  theme: {
    extend: {
      colors: {
        'purple-700': '#1F01B9', // Figma 设计稿中的主色调
        'blue-600': '#2563eb', // Artin 主色调蓝色
        'blue-700': '#1d4ed8', // 深蓝色
        'gray-50': '#f9fafb', // 浅灰背景
        'gray-600': '#4b5563', // 文字灰色
        'gray-800': '#1f2937' // 深灰文字
      },
      spacing: {
        88: '22rem', // 352px / 16 = 22rem
        42: '10.5rem', // 168px / 16 = 10.5rem
        33: '8.25rem', // 132px / 16 = 8.25rem
        18: '4.5rem', // 72px / 16 = 4.5rem
        15: '3.75rem' // 60px / 16 = 3.75rem
      },
      gridTemplateColumns: {
        2: 'repeat(2, minmax(0, 1fr))'
      },
      borderRadius: {
        '2xl': '1rem', // 16px
        '3xl': '1.5rem' // 24px
      },
      // 添加自定义背景图像，避免CSS变量问题
      backgroundImage: {
        'gradient-light': 'linear-gradient(to bottom, #F6F7FF, #F3F4F9)',
        'gradient-red': 'linear-gradient(to bottom, #FF7171, #FF5050)',
        'gradient-blue': 'linear-gradient(to bottom, #717CFF, #5B50FF)',
        'gradient-zheng': 'linear-gradient(122deg, #F0F4CB 0%, #C7F2F3 100%)'
      }
    }
  },
  // 其他配置项 ...
  corePlugins: {
    // 小程序不需要 preflight，因为这主要是给 h5 的，如果你要同时开发多端，你应该使用 process.env.TARO_ENV 环境变量来控制它
    preflight: false
  }
}
