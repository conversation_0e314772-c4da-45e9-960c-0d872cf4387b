import { authorityState } from '@/store/global'
import { useAtomValue } from 'jotai'

export const useAuthorityVerify = (code: string | undefined) => {
  const authority = useAtomValue(authorityState)

  const has = () => {
    const codes = (code || '')
      .split(',')
      .filter((v) => v)
      .map((v) => `${v}:1`)
    let state = false
    for (let i = 0; i < codes.length; i++) {
      if (authority.includes(codes[i])) {
        state = true
        break
      }
    }
    return state
  }
  return has()
}

export const getAuthorityVerify = (code: string | undefined, authority: string) => {
  const has = () => {
    const codes = (code || '')
      .split(',')
      .filter((v) => v)
      .map((v) => `${v}:1`)
    let state = false
    for (let i = 0; i < codes.length; i++) {
      if (authority.includes(codes[i])) {
        state = true
        break
      }
    }
    return state
  }
  return has()
}
