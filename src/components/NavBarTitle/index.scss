.navController {
  position: fixed;
  z-index: 20;
  top: 0;
  left: 0;
  width: 100%;
  display: flex;
  align-items: flex-end;
  justify-content: center;
  .navTools {
    position: absolute;
    left: 0;
    bottom: 0;
    height: 96px;
    // background-color: #ddd;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    .navTypeNormal {
      width: 64px;
      height: 64px;
      display: flex;
      align-items: center;
      justify-content: center;
      image {
        width: 40px;
        height: 40px;
      }
    }
    .navTypeHome {
      margin-left: 20px;
      display: flex;
      align-items: center;
      justify-content: space-evenly;
      width: 174px;
      height: 56px;
      background: rgba(255, 255, 255, 0.6);
      border-radius: 32px;
      border: 1px solid rgba(151, 151, 151, 0.2);
      .separate {
        width: 2px;
        height: 40px;
        background: rgba(0, 0, 0, 0.2);
      }
      image {
        width: 40px;
        height: 40px;
      }
    }
    .navTypeWhite {
      margin-left: 20px;
      display: flex;
      align-items: center;
      justify-content: space-evenly;
      width: 174px;
      height: 56px;
      background: transparent;
      border-radius: 32px;
      border: 1px solid gray;
      .separate {
        width: 2px;
        height: 40px;
        background: rgba(255, 255, 255, 0.2);
      }
      .dark {
        background: rgba(0, 0, 0, 0.2);
      }
      image {
        width: 40px;
        height: 40px;
      }
    }
  }
  .iconBox {
    width: 64px;
    height: 56px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .navTitle {
    font-size: 36px;
    font-family: Kaiti SC;
    height: 96px;
    line-height: 96px;
    text-align: center;
    width: 320px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
}
.shadow {
  box-shadow: rgba(0, 0, 0, 0.16) 0px 1px 4px;
}
