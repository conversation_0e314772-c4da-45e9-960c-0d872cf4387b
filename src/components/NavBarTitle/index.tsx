import Taro, { usePageScroll, useRouter } from '@tarojs/taro'
import { useEffect, useState } from 'react'
import { View, Image } from '@tarojs/components'

import arrow from '@/assets/images/navbar/arrow_01.png'
import goback from '@/assets/images/navbar/return1.png'
import home from '@/assets/images/navbar/home.png'
import goback_dark from '@/assets/images/navbar/goback_dark.png'
import home_dark from '@/assets/images/navbar/home_dark.png'

import './index.scss'

interface IProps {
  scrollTop?: number // 滚动距离
  bgColor?: string // 自定义背景颜色
  titleStyle?: any // 字体样式
  type?: 'normal' | 'home' | 'white' | 'none' // 类型 正常 带返回  没有
  roll?: boolean // 支持滚动透明滚动变白底，标题也变色
  shadow?: boolean // 阴影
  text: string
  getHeight?: Function
  hideTitle?: boolean // 隐藏title
  backEvent?: Function
  fill?: boolean // 是否填充
}
const NavBarTitle = ({
  scrollTop = 0,
  bgColor = '',
  titleStyle = {},
  type = 'normal',
  roll = false,
  shadow = false,
  getHeight,
  text = '',
  hideTitle = false,
  backEvent,
  fill = false
}: IProps) => {
  const [height, setHeight] = useState(0)
  const [textState, setTextState] = useState(false)
  const [localBgColor, setLocalBgColor] = useState('')

  useEffect(() => {
    if (Taro.getEnv() === 'WEAPP') {
      const rect = Taro.getMenuButtonBoundingClientRect()
      const { top } = rect || {}
      setHeight(top + 42)
      getHeight && getHeight(top)
    } else {
      setHeight(100)
      getHeight && getHeight(58)
    }
  }, [])

  useEffect(() => {
    setLocalBgColor(bgColor)
  }, [bgColor])

  useEffect(() => {
    const ratio = scrollTop / height
    if (roll) {
      setLocalBgColor(`rgba(255,255,255,${ratio})`)
    }
    if (ratio > 0.5) {
      setTextState(true)
    } else {
      setTextState(false)
    }
  }, [scrollTop, height, roll])

  const back = () => {
    Taro.navigateBack({ delta: 1 })
    backEvent && backEvent()
  }

  const toHome = () => {
    Taro.reLaunch({
      url: '/pages/index/index'
    })
    backEvent && backEvent()
  }

  return (
    <>
      <View
        className={`navController ${shadow ? (!roll ? 'shadow' : textState ? 'shadow' : '') : ''}`}
        style={{ height: `${height}px`, backgroundColor: localBgColor }}
      >
        <View className="navTools">
          {type === 'normal' ? (
            <View className="navTypeNormal" onClick={back}>
              <Image src={arrow}></Image>
            </View>
          ) : null}
          {type === 'home' ? (
            <View className="navTypeHome">
              <View className="iconBox" onClick={back}>
                <Image src={goback}></Image>
              </View>
              <View className="separate"></View>
              <View className="iconBox" onClick={toHome}>
                <Image src={home}></Image>
              </View>
            </View>
          ) : null}
          {type === 'white' ? (
            <View className="navTypeWhite">
              <View className="iconBox" onClick={back}>
                <Image src={roll && textState ? goback : goback_dark}></Image>
              </View>
              <View className={roll && textState ? 'separate dark' : 'separate'}></View>
              <View className="iconBox" onClick={toHome}>
                <Image src={roll && textState ? home : home_dark}></Image>
              </View>
            </View>
          ) : null}
        </View>
        <View className="navTitle" style={{ color: roll && !textState ? '#FFF' : '#000', ...titleStyle }}>
          {hideTitle && !textState ? '' : text}
        </View>
      </View>
      {fill && <View style={{ height: `${height}px` }}></View>}
    </>
  )
}

export default NavBarTitle
