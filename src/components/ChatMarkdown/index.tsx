import { memo, useMemo } from 'react'
import Markdown from 'react-markdown'
import remarkBreaks from 'remark-breaks'

export const ChatMarkdown = memo(({ content, position }: { content: string; position: 'left' | 'right' }) => {
  // 将字符串中的 \n 转换为真实的换行符
  const processedContent = useMemo(() => {
    // 如果内容中包含 \n 字符序列，将其替换为真实的换行符
    if (content && content.includes('\\n')) {
      return content.replace(/\\n/g, '\n')
    }
    return content
  }, [content])

  return (
    <Markdown
      remarkPlugins={[remarkBreaks]}
      components={{
        h3(props) {
          const { node, ...rest } = props
          return <h3 className="my-[10px] font-semibold text-[36px]" {...rest} />
        },
        p(props) {
          const { node, ...rest } = props
          return (
            <p
              className={`my-[10px] text-[28px] leading-[1.3] flex  ${position === 'right' ? 'justify-end' : 'justify-start'}`}
              {...rest}
            />
          )
        },
        li(props) {
          const { node, ...rest } = props
          return <li className="my-[5px] text-[28px] ml-[40px] leading-[1.3]" {...rest} />
        },
        img(props) {
          const { node, ...rest } = props
          return <img className="max-w-[50%] h-auto" {...rest} />
        }
      }}
    >
      {processedContent}
    </Markdown>
  )
})
