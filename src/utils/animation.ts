/**
 * 小程序动画工具函数
 * 解决小程序环境中时间API兼容性问题
 */

/**
 * 获取当前时间戳 - 小程序兼容版本
 * 避免使用 performance.now() 和 Date.now() 在小程序中的兼容性问题
 */
export const getCompatibleTime = (): number => {
  try {
    // 优先使用 performance.now()
    if (typeof performance !== 'undefined' && performance.now) {
      return performance.now()
    }
    
    // 备用方案：使用 Date.now()
    if (typeof Date !== 'undefined' && Date.now) {
      return Date.now()
    }
    
    // 最后备用方案：使用 new Date().getTime()
    return new Date().getTime()
  } catch (error) {
    console.warn('时间API获取失败，使用备用方案:', error)
    // 如果所有时间API都失败，返回一个基于计数器的时间
    return getCounterTime()
  }
}

// 计数器时间 - 当所有时间API都不可用时的备用方案
let timeCounter = 0
let lastCounterTime = 0

const getCounterTime = (): number => {
  const now = timeCounter++
  const deltaTime = now - lastCounterTime
  lastCounterTime = now
  return now * 16.67 // 模拟 60fps 的时间间隔
}

/**
 * 创建兼容的动画循环
 * 在小程序环境中提供稳定的动画帧率
 */
export const createCompatibleAnimationLoop = (
  callback: () => void,
  fps: number = 60
): (() => void) => {
  let animationId: number | null = null
  let isRunning = false
  
  // 计算帧间隔
  const frameInterval = 1000 / fps
  let lastFrameTime = 0
  
  const animate = () => {
    if (!isRunning) return
    
    const currentTime = getCompatibleTime()
    
    // 控制帧率
    if (currentTime - lastFrameTime >= frameInterval) {
      callback()
      lastFrameTime = currentTime
    }
    
    // 继续下一帧
    if (isRunning) {
      animationId = requestAnimationFrame(animate)
    }
  }
  
  // 启动动画
  const start = () => {
    if (isRunning) return
    isRunning = true
    lastFrameTime = getCompatibleTime()
    animationId = requestAnimationFrame(animate)
  }
  
  // 停止动画
  const stop = () => {
    isRunning = false
    if (animationId !== null) {
      cancelAnimationFrame(animationId)
      animationId = null
    }
  }
  
  // 立即启动
  start()
  
  // 返回停止函数
  return stop
}

/**
 * 创建固定间隔的动画循环 - 小程序专用
 * 使用固定的时间间隔，避免时间API的不稳定性
 */
export const createFixedIntervalAnimation = (
  callback: () => void,
  intervalMs: number = 16.67 // 默认 60fps
): (() => void) => {
  let animationId: number | null = null
  let isRunning = false
  
  const animate = () => {
    if (!isRunning) return
    
    callback()
    
    // 继续下一帧
    if (isRunning) {
      animationId = requestAnimationFrame(animate)
    }
  }
  
  // 启动动画
  const start = () => {
    if (isRunning) return
    isRunning = true
    animationId = requestAnimationFrame(animate)
  }
  
  // 停止动画
  const stop = () => {
    isRunning = false
    if (animationId !== null) {
      cancelAnimationFrame(animationId)
      animationId = null
    }
  }
  
  // 立即启动
  start()
  
  // 返回停止函数
  return stop
}

/**
 * 计算动画移动距离 - 小程序兼容版本
 * 使用固定的时间间隔来确保稳定的动画效果
 */
export const calculateMoveDistance = (
  speed: number, // px/s
  fps: number = 60
): number => {
  const frameInterval = 1000 / fps // ms
  return (speed * frameInterval) / 1000
}
