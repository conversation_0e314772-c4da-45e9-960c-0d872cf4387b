import OSS from 'ali-oss'

import { getSts, getSts2 } from '@/api/global'

import { getUUID } from './tool'

class UploadClient {
  constructor() {}

  public async getOSSClient(sts = 1) {
    const stsFn = sts === 1 ? getSts : getSts2
    const res = await stsFn()
    const { credentials, cdnUrl, bucketName, preUrl, endpoint } = res.data
    const { securityToken, accessKeySecret, accessKeyId, expiration } = credentials
    const OSSClientData = {
      bucket: bucketName,
      region: endpoint.split('//')[1].split('.')[0],
      accessKeyId,
      accessKeySecret,
      stsToken: securityToken,
      expiration,
      refreshSTSToken: async () => {
        const res = await stsFn()
        const { credentials } = res.data
        const { securityToken, accessKeySecret, accessKeyId } = credentials
        return {
          accessKeyId,
          accessKeySecret,
          stsToken: securityToken
        }
      }
    }
    const OSSClient = new OSS(OSSClientData)
    return { OSSClient, preUrl, cdnUrl }
  }

  public async fileUpload(files: File[], fileNames?: string[], sts = 1) {
    const { OSSClient, preUrl, cdnUrl } = await this.getOSSClient(sts)
    const promises = files.map(async (file, i) => {
      const uuid = getUUID()
      const filePath = fileNames?.length ? `${preUrl}/${fileNames[i]}` : `${preUrl}/${uuid}/${file.name}`
      try {
        let percentNum = 0
        await OSSClient.multipartUpload(filePath, file, {
          progress: (percent) => {
            percentNum = percent * 100
          }
        })
        return {
          name: file.name,
          url: `${cdnUrl}/${filePath}`,
          percent: percentNum
        }
      } catch (error) {
        console.error(`Failed to upload file: ${file.name}`, error)
        throw error
      }
    })
    return await Promise.all(promises)
  }
}

export default new UploadClient()
