import serveImg from '@/assets/images/detail/serve.png'
import designImg from '@/assets/images/detail/design.png'
import Taro from '@tarojs/taro'
import { ReadyTemplateSaveRes } from '@/api/detail'

const SubmitBtn = ({ data, onCreateOrder }: { data: ReadyTemplateSaveRes['data']; onCreateOrder: () => void }) => {
  // data.customNum是人数，转换为w人，保留2位小数
  const customNum = (data.customNum / 10000).toFixed(1)

  return (
    <>
      <div className="h-[176px] fixed bottom-0 left-0 w-full submit_btn_shadow">
        <div className="flex_center h-[80px] mt-[20px]">
          <div className="flex_center flex-col w-[52px] mr-[26px]">
            <img className="w-[52px] h-[52px]" src={serveImg} alt="" />
            <div className="font-normal text-[18px] text-[#202020] leading-[26px] text-left not-italic">客服</div>
          </div>
          <div
            onClick={() => {
              Taro.navigateTo({
                url: `/pages/canvas/index?id=${data.id}`
              })
            }}
            className="w-[310px] h-[80px] rounded-[16px] border-2 border-solid border-[#303335] flex_center mr-[12px]"
          >
            <img className="w-[42px] h-[42px]" src={designImg} alt="" />
            <div className="mx-[6px_4px] font-medium text-[28px] text-black leading-[28px] text-center not-italic">设计同款</div>
            <div className="mx-[6px_4px] font-medium text-[28px] text-black leading-[28px] text-center not-italic">{customNum}万人</div>
          </div>
          <div
            onClick={() => {
              // Taro.navigateTo({
              //   url: `/pages/pay/index?id=${data.id}`
              // })
              onCreateOrder()
            }}
            className="bg-[#000000] w-[310px] h-[80px] rounded-[16px] font-medium text-[28px] text-white leading-[24px] text-center not-italic flex_center"
          >
            立即购买
          </div>
        </div>
      </div>
      <div className="h-[176px]"></div>
    </>
  )
}

export default SubmitBtn
