import { Swiper, SwiperItem, View } from '@tarojs/components'
import { useEffect, useState, forwardRef, useImperativeHandle } from 'react'
import demoImg from '@/assets/images/inspiration/demo.png'
import zhengImg from '@/assets/images/detail/zheng.png'
import { CanvasRes, ColourMap, computePrice, ComputePriceRes, GenderMap, getCanvas, ReadyTemplateSaveRes, templateSave } from '@/api/detail'
import { useAsyncFn, useUpdateEffect } from 'react-use'
import useObjState from '@/hooks/useObjState'

export interface GoodsRef {
  templateSaveFetch: () => Promise<any>
}

// 辅助函数：根据颜色名称返回对应的十六进制颜色代码
function getColorHex(color: string) {
  const colorMap: Record<string, string> = {
    白色: '#ffffff',
    黑色: '#000000',
    紫色: '#800080',
    绿色: '#008000'
  }
  return colorMap[color] || '#ffffff'
}

const Goods = forwardRef<GoodsRef, { id: string; data: ReadyTemplateSaveRes['data'] }>(({ id, data }, ref) => {
  console.log('id, data', id, data)
  const imageUrl = data.imageUrl.split('，')
  const [activeGender, setActiveGender] = useState(data.gender)
  const [activeColor, setActiveColor] = useState(data.colour)
  const [activePrint, setActivePrint] = useState(data.printingProcessId)
  const styles = useObjState<CanvasRes['data']['genders']>([])
  const colors = useObjState<CanvasRes['data']['colours']>([])
  const prints = useObjState<CanvasRes['data']['printingProcessList']>([])
  const compute = useObjState<ComputePriceRes['data'] | null>(null)

  const [canvasState, canvasFetch] = useAsyncFn(async () => {
    const res = await getCanvas(data.templateCode)
    console.log('response', res)
    styles.set(res.data.genders)
    colors.set(res.data.colours)
    prints.set(res.data.printingProcessList)
    return res
  }, [])

  const [computePriceState, computePriceFetch] = useAsyncFn(async () => {
    const diyData = JSON.parse(data.diyData)
    const res = await computePrice({
      gender: activeGender,
      masterTemplateCode: data.templateCode,
      styleTemplateCode: data.styleCode,
      frontDiyData: JSON.stringify(diyData[0]) || '',
      backDiyData: JSON.stringify(diyData[1]) || '',
      frontPrintingId: activePrint,
      backPrintingId: activePrint,
      buyNum: 1,
      sizeCode: 'S'
    })
    console.log('response', res)
    compute.set(res.data)
    return res
  }, [activeGender, activePrint])

  useEffect(() => {
    canvasFetch()
  }, [])

  useUpdateEffect(() => {
    computePriceFetch()
  }, [activeGender, activePrint])

  const [templateSaveState, templateSaveFetch] = useAsyncFn(async () => {
    const currentComputeData = compute.val || data.computePriceData

    const params = {
      title: data.title,
      imageUrl: data.imageUrl,
      price: currentComputeData.totalPrice,
      diyData: data.diyData,
      templateCode: data.templateCode,
      styleCode: data.styleCode,
      colour: activeColor,
      gender: activeGender,
      tagIds: data.tagList?.map((tag) => tag.id),
      printingImage: data.imageUrl,
      computePriceData: currentComputeData
    }

    const res = await templateSave(params)
    return res
  }, [data, activeColor, activeGender, activePrint, compute.val])

  useImperativeHandle(
    ref,
    () => ({
      templateSaveFetch
    }),
    [templateSaveFetch]
  )

  return (
    <>
      <Swiper className="test-h h-[812px]" indicatorColor="#999" indicatorActiveColor="#333" circular indicatorDots autoplay>
        {imageUrl.map((item) => (
          <SwiperItem key={item}>
            <View className="demo-text-1 bg-slate-100 h-[812px]">
              <img className="w-[812px] h-[812px] object-contain" src={item} alt="" />
            </View>
          </SwiperItem>
        ))}
      </Swiper>

      <div className="bg-white px-[18px]">
        <div className="pt-[30px]">
          {/* 款式选择 */}
          <div className="mb-[30px] flex items-center">
            <div className="w-[110px] h-[60px] flex_center font-normal text-[24px] text-[#202020] opacity-50 leading-[34px] tracking-[2px] text-left not-italic ml-[10px] mr-[18px]">
              T恤款式
            </div>
            <div className="flex">
              {styles.val.map((style) => (
                <div
                  key={style}
                  onClick={() => setActiveGender(style)}
                  className={`mr-[12px] w-[120px] h-[60px] rounded-[8px] border-2 border-solid flex_center font-normal text-[24px] text-black leading-[34px] text-left not-italic ${activeGender === style ? 'border-[#000000] opacity-100' : 'border-[rgba(0,0,0,0.1)] opacity-50'}`}
                >
                  {GenderMap[style]}
                </div>
              ))}
            </div>
          </div>

          {/* 颜色选择 */}
          <div className="mb-[30px] flex items-center">
            <div className="w-[110px] h-[60px] flex_center font-normal text-[24px] text-[#202020] opacity-50 leading-[34px] tracking-[2px] text-left not-italic ml-[10px] mr-[18px]">
              T恤颜色
            </div>
            <div className="flex">
              {colors.val.map((color) => (
                <div
                  key={color}
                  onClick={() => setActiveColor(color)}
                  className={`mr-[12px] w-[120px] h-[60px] rounded-[8px] border-2 border-solid flex_center ${activeColor === color ? 'border-[#000000]' : 'border-[rgba(0,0,0,0.1)]'}`}
                >
                  <div
                    className="color_shadow w-[24px] h-[24px] rounded-full mr-[6px]"
                    style={{ backgroundColor: ColourMap[color].color }}
                  ></div>
                  <div
                    className={`font-normal text-[24px] text-black leading-[34px] text-left not-italic ${activeColor === color ? 'opacity-100' : 'opacity-50'}`}
                  >
                    {ColourMap[color].title}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* 印花工艺 */}
          <div className="mb-[40px] flex items-start">
            <div className="w-[110px] h-[60px] flex_center font-normal text-[24px] text-[#202020] opacity-50 leading-[34px] tracking-[2px] text-left not-italic ml-[10px] mr-[18px] flex-shrink-0">
              印花工艺
            </div>
            <div className="flex-1 w-0">
              <div className="overflow-x-auto overflow-y-hidden flex no-scrollbar">
                {prints.val.map((print) => (
                  <div
                    key={print.name}
                    onClick={() => setActivePrint(print.id)}
                    className={`mr-[12px] w-[188px] overflow-hidden h-[160px] rounded-[8px] border-2 border-solid flex_center flex-col flex-shrink-0 ${activePrint === print.id ? 'border-[#000000]' : 'border-[rgba(0,0,0,0.1)]'}`}
                  >
                    <img className="w-[188px] h-[106px] object-contain" src={print.imageUrl} alt="" />
                    <div
                      className={`flex-1 w-full font-normal text-[24px] text-black leading-[34px] flex_center not-italic ${activePrint === print.id ? 'opacity-100' : 'opacity-50'}`}
                    >
                      <div className="mr-[18px]">{print.name}</div>
                      <div className="text-[18px]">¥{print.price / 100}</div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* 价格 */}
          <div className="w-[716px] h-[306px] rounded-[16px] bg-[#F8F8F8] flex flex-col mb-[40px]">
            <div className="flex-1 h-0 px-[38px] flex flex-col justify-center">
              <div className="mb-[26px] flex items-end">
                <div className="font-semibold text-[40px] text-[#E40032] leading-none text-left not-italic">¥</div>
                <div className="font-semibold text-[60px] text-[#E40032] leading-none text-left not-italic">
                  {(compute.val?.totalPrice || data.computePriceData.totalPrice) / 100}
                </div>
                <div className="font-normal text-[24px] text-[#202020] leading-[34px] text-left not-italic line-through opacity-50 ml-[10px]">
                  优惠前¥{data.delPrice / 100}
                </div>
                <div className="flex-1 text-right font-normal text-[24px] text-[#202020] leading-[34px] not-italic opacity-50">
                  已售800+
                </div>
              </div>
              <div className="font-semibold text-[36px] text-[#202020] leading-[50px] text-left not-italic mb-[6px]">{data.title}</div>
              <div className="font-normal text-[24px] text-[#202020] leading-[34px] text-left not-italic opacity-50">{data.des}</div>
            </div>
            <div className="w-full h-[76px] rounded-[0px_0px_16px_16px] bg-gradient-zheng flex items-center">
              <img className="w-[42px] h-[42px] ml-[34px] mr-[4px]" src={zhengImg} alt="" />
              <div className="font-normal text-[24px] text-[#202020] leading-[34px] text-left not-italic">
                甄选好料 · 精准还原 · 潮流设计 · 贴心售后
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  )
})

export default Goods
