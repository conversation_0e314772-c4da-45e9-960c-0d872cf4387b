import useObj<PERSON><PERSON> from '@/hooks/useObjAtom'
import { recommendListState } from '@/store/detail'

const Recommend = () => {
  const recommendList = useObjAtom(recommendListState)

  return (
    <div className="flex flex-col h-full">
      <div className="mt-[20px] mb-[32px] w-full font-normal text-[20px] text-[#202020] leading-[44px] opacity-50 not-italic flex_center">
        -为你推荐-
      </div>
      <div className="flex-1">
        <div className="flex flex-wrap gap-[12px] px-[19px]">
          {recommendList.val.map((item) => (
            <div key={item.id} className="w-[350px] h-[410px] overflow-hidden flex flex-col mb-[10px] justify-between">
              <img className="rounded-[16px] w-[350px] h-[350px]" src={item.img} alt={item.name} />
              <div className="flex justify-between items-end pl-[4px] pr-[10px]">
                <div className="font-normal text-[28px] text-black leading-[40px] text-left not-italic">{item.name}</div>
                <div className="font-normal text-[20px] text-black leading-[24px] not-italic flex items-end">
                  ￥<span className="text-[32px] text-black leading-[32px] not-italic">{item.price}</span>
                </div>
              </div>
            </div>
          ))}
        </div>
        {/* - END - */}
        {/* <div className="my-[38px] w-full font-normal text-[20px] text-[#202020] leading-[44px] not-italic flex_center">- END -</div> */}
      </div>
    </div>
  )
}

export default Recommend
