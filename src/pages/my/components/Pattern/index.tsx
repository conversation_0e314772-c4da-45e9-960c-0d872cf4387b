import useObj<PERSON>tom from '@/hooks/useObjAtom'
import { patternListState } from '@/store/inspiration'

import Taro from '@tarojs/taro'

const Pattern = () => {
  const patternList = useObjAtom(patternListState)

  return (
    <div className="flex flex-col h-full">
      <div className="overflow-auto flex-1">
        <div className="flex flex-wrap gap-[12px] p-[19px_19px_0]">
          {patternList.val.map((item) => (
            <div
              key={item.id}
              onClick={() => {
                Taro.navigateTo({
                  url: '/pages/pattern-detail/index'
                })
              }}
              className="w-[350px] h-[430px] overflow-hidden bg-white flex flex-col"
            >
              <img className="w-[350px] h-[350px] rounded-[16px]" src={item.img} alt={item.name} />
              <div className="flex-1 flex flex-col justify-center">
                <div className="font-normal text-[28px] text-black leading-[40px] not-italic mb-[8px] line-clamp-1">{item.name}</div>
              </div>
            </div>
          ))}
        </div>
        {/* - END - */}
        <div className="my-[38px] w-full font-normal text-[20px] text-[#202020] leading-[44px] not-italic flex_center">- END -</div>
      </div>
    </div>
  )
}

export default Pattern
