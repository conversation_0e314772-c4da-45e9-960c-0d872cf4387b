import use<PERSON>bj<PERSON><PERSON> from '@/hooks/useObjAtom'
import collect1Img from '@/assets/images/inspiration/collect1.png'
import Taro from '@tarojs/taro'
import { collectListState } from '@/store/my'
import { useEffect } from 'react'
import { collectImageList } from '@/api/my'
import { useAsyncFn } from 'react-use'

const Collect = () => {
  const collectList = useObjAtom(collectListState)

  const [collectImageListState, collectImageListFetch] = useAsyncFn(async () => {
    const res = await collectImageList({
      lastId: '',
      size: '20'
    })
    console.log('response', res)
    collectList.set(res.data)
    return res
  }, [])

  useEffect(() => {
    collectImageListFetch()
  }, [])

  return (
    <div className="flex flex-col h-full">
      <div className="overflow-auto flex-1">
        <div className="flex flex-wrap gap-[12px] p-[19px_19px_0]">
          {collectList.val.map((item) => (
            <div
              key={item.id}
              onClick={() => {
                Taro.navigateTo({
                  url: '/pages/pattern-detail/index'
                })
              }}
              className="w-[350px] h-[540px] rounded-[16px] overflow-hidden bg-[#F8F8F8] flex flex-col"
            >
              <img className="w-[350px] h-[350px] rounded-[16px_16px_0px_0px]" src={item.imageUrl} alt={item.description} />
              <div className="flex-1 flex flex-col justify-center px-[16px]">
                <div className="font-normal text-[24px] text-[#202020] leading-[36px] text-justify not-italic mb-[20px] line-clamp-2">
                  {item.description}
                </div>
                <div className="flex justify-between items-end">
                  <div className="w-[60px] h-[60px] rounded-[16px] bg-white flex_center">
                    <img className="w-[42px] h-[40px]" src={collect1Img} alt="" />
                  </div>
                  <div className="w-[238px] h-[60px] rounded-[16px] bg-white flex_center">
                    <span className="font-medium text-[20px] text-black leading-none text-center not-italic mr-[8px]">设计同款</span>
                    <span className="font-normal text-[16px] text-black leading-none text-center not-italic">{item.customNum}万人</span>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
        {/* - END - */}
        <div className="my-[38px] w-full font-normal text-[20px] text-[#202020] leading-[44px] not-italic flex_center">- END -</div>
      </div>
    </div>
  )
}

export default Collect
