import useObj<PERSON>tom from '@/hooks/useObjAtom'
import { productListState } from '@/store/inspiration'
import Taro from '@tarojs/taro'

const Product = () => {
  const productList = useObjAtom(productListState)

  return (
    <div className="flex flex-col h-full">
      <div className="overflow-auto flex-1">
        <div className="flex flex-wrap gap-[12px] p-[19px_19px_0]">
          {productList.val.map((item) => (
            <div
              key={item.id}
              onClick={() => {
                Taro.navigateTo({
                  url: '/pages/detail/index'
                })
              }}
              className="w-[350px] h-[430px] overflow-hidden bg-white flex flex-col"
            >
              <img className="w-[350px] h-[350px] rounded-[16px]" src={item.imageUrl} alt={item.title} />
              <div className="flex-1 flex flex-col justify-center">
                <div className="font-normal text-[28px] text-black leading-[40px] not-italic mb-[8px]">{item.title}</div>
              </div>
            </div>
          ))}
        </div>
        {/* - END - */}
        <div className="my-[38px] w-full font-normal text-[20px] text-[#202020] leading-[44px] not-italic flex_center">- END -</div>
      </div>
    </div>
  )
}

export default Product
