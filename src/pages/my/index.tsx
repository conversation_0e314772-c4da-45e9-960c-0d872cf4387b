import useObj<PERSON>tom from '@/hooks/useObjAtom'
import Taro from '@tarojs/taro'
import { ActiveTabEnum, ActiveTabs, activeTabState } from '@/store/my'
import demoImg from '@/assets/images/inspiration/demo.png'
import orderImg from '@/assets/images/my/order.png'
import couponsImg from '@/assets/images/my/coupons.png'
import connectImg from '@/assets/images/my/connect.png'
import settingImg from '@/assets/images/my/setting.png'
import Product from './components/Product'
import Pattern from './components/Pattern'
import Collect from './components/Collect'

const Index = () => {
  const activeTab = useObjAtom(activeTabState)
  const isWx = Taro.getEnv() === Taro.ENV_TYPE.WEAPP

  return (
    <>
      <div className="w-full h-screen overflow-hidden relative flex flex-col">
        <div className="absolute z-[1] left-0 top-0 flex flex-col justify-end px-[30px] h-[520px] w-full box-border bg-black">
          <div className="flex_center mb-[52px] w-full">
            <div className="w-[120px] h-[120px] mr-[20px]">
              <img className="w-[120px] h-[120px] rounded-full" src={demoImg} alt="" />
            </div>
            <div className="flex-1 flex flex-col justify-center">
              <div className="font-semibold text-[36px] text-white leading-[50px] text-left not-italic mb-[6px]">用户姓名</div>
              <div className="font-normal text-[24px] text-white leading-[34px] text-left not-italic opacity-50">账号：136****6537</div>
            </div>
            <div className="w-[48px] h-[48px] anim_btn">
              <img className="w-[48px] h-[48px]" src={settingImg} alt="" />
            </div>
          </div>
          <div className="mb-[70px] flex">
            <div
              onClick={() => {
                Taro.navigateTo({
                  url: '/pages/order/index'
                })
              }}
              className="w-[178px] h-[92px] rounded-[20px] bg-[#FFFFFF25] flex_center mr-[12px] anim_btn"
            >
              <img className="mr-[8px] w-[30px] h-[30px]" src={orderImg} alt="" />
              <div className="font-medium text-[22px] text-white leading-[32px] text-left not-italic">我的订单</div>
            </div>
            <div className="w-[178px] h-[92px] rounded-[20px] bg-[#FFFFFF25] flex_center mr-[12px] anim_btn">
              <img className="mr-[8px] w-[30px] h-[30px]" src={couponsImg} alt="" />
              <div className="font-medium text-[22px] text-white leading-[32px] text-left not-italic">优惠券</div>
            </div>
            <div className="w-[178px] h-[92px] rounded-[20px] bg-[#FFFFFF25] flex_center mr-[12px] anim_btn">
              <img className="mr-[8px] w-[30px] h-[30px]" src={connectImg} alt="" />
              <div className="font-medium text-[22px] text-white leading-[32px] text-left not-italic">联系客服</div>
            </div>
          </div>
        </div>
        <div className="relative z-10 overflow-auto flex-1 h-0 pointer-events-none">
          <div className="h-[490px] pointer-events-none"></div>

          <div className="bg-white rounded-[24px_24px_0_0] pointer-events-auto">
            <div className="flex items-center h-[108px] w-full">
              <div className="flex-1 flex_center gap-[52px]">
                {ActiveTabs.map((item) => {
                  return (
                    <div
                      key={item.tab}
                      className={`font-medium text-[32px] text-black leading-[44px] not-italic relative ${
                        activeTab.val === item.tab
                          ? "opacity-100 after:content-[''] after:absolute after:w-[24px] after:rounded-[3px] after:h-[4px] after:bg-black after:bottom-[-6px] after:left-[20px]"
                          : 'opacity-50'
                      }`}
                      onClick={() => {
                        activeTab.set(item.tab)
                      }}
                    >
                      {item.name}
                    </div>
                  )
                })}
              </div>
            </div>
            <div className="relative flex-1 overflow-auto">
              {activeTab.val === ActiveTabEnum.product ? <Product /> : null}
              {activeTab.val === ActiveTabEnum.pattern ? <Pattern /> : null}
              {activeTab.val === ActiveTabEnum.collect ? <Collect /> : null}
            </div>
          </div>
        </div>
        {isWx ? <div className="h-[154px]"></div> : null}
      </div>
    </>
  )
}

export default Index
