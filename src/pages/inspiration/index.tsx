import { useEffect } from 'react'
import useObj<PERSON><PERSON> from '@/hooks/useObjAtom'
import { activeProductGroupIdState, activeSubTagsState, activeTabState, inspirationTabsState, productListState } from '@/store/inspiration'
import { useAtomValue } from 'jotai'
import { readyTemplateList, tagList } from '@/api/inspiration'
import { useAsyncFn } from 'react-use'
import NavBarTitle from './components/NavBarTitle'
import Pattern from './components/Pattern'
import Product from './components/Product'

const Index = () => {
  const inspirationTabs = useObjAtom(inspirationTabsState)
  const activeTab = useObjAtom(activeTabState)
  const activeSubTags = useAtomValue(activeSubTagsState)
  const activeProductGroupId = useObjAtom(activeProductGroupIdState)
  const productList = useObjAtom(productListState)

  const [tagListState, tagListFetch] = useAsyncFn(async () => {
    const res = await tagList()
    console.log('response', res)
    inspirationTabs.set(res.data.tags)
    activeTab.set(res.data.tags[0].id)
    activeProductGroupId.set(res.data.tags[0].subTags[0].id)
    return res
  }, [])

  const [readyTemplateListState, readyTemplateListFetch] = useAsyncFn(async () => {
    const res = await readyTemplateList({
      parentTagId: activeTab.get(),
      subTagId: activeProductGroupId.get(),
      pageNum: 1,
      sizeNum: 10
    })
    console.log('response', res)
    productList.set(res.data.list)
    return res
  }, [])

  useEffect(() => {
    tagListFetch()
  }, [])

  useEffect(() => {
    activeSubTags.length && activeProductGroupId.set(activeSubTags[0].id)
  }, [activeSubTags, activeTab.val])

  useEffect(() => {
    readyTemplateListFetch()
  }, [activeTab.val, activeProductGroupId.val])

  return (
    <>
      <div className="w-full h-screen bg-[#F8F8F8] overflow-hidden flex flex-col">
        <NavBarTitle />
        <div className="relative flex-1 overflow-auto">
          <div className="flex flex-col h-full">
            <div className="h-[50px] w-full pb-[28px]">
              <div className="flex gap-[20px] h-[60px] items-center overflow-auto px-[30px] no-scrollbar">
                {activeSubTags.map((item) => (
                  <div
                    key={item.id}
                    className={`px-[20px] flex_center h-full rounded-full whitespace-nowrap flex-shrink-0 min-w-auto ${activeProductGroupId.val === item.id ? 'bg-black' : 'bg-transparent'}`}
                    onClick={() => {
                      activeProductGroupId.set(item.id)
                    }}
                  >
                    <div
                      className={`font-medium text-[26px] leading-[24px] text-left not-italic ${activeProductGroupId.val === item.id ? 'opacity-100 text-white' : 'opacity-60 text-black'}`}
                    >
                      {item.name}
                    </div>
                  </div>
                ))}
              </div>
            </div>
            <div className="overflow-auto flex-1">
              <div className="flex flex-wrap gap-[12px] p-[19px_19px_0]">
                {productList.val.map((item) =>
                  item.uiCode === 'GARMENT' ? <Product key={item.id} item={item} /> : <Pattern key={item.id} item={item} />
                )}
              </div>
              {/* - END - */}
              <div className="my-[38px] w-full font-normal text-[20px] text-[#202020] leading-[44px] not-italic flex_center">
                {productList.val.length ? '- END -' : '- No Data -'}
              </div>
            </div>
          </div>
        </div>
        <div className="h-[154px]"></div>
      </div>
    </>
  )
}

export default Index
