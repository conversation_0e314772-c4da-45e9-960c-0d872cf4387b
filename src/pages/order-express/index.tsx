import { queryExpressTrack } from '@/api/order'
import { useRouter } from '@tarojs/taro'
import { useEffect } from 'react'
import { useAsyncFn } from 'react-use'
import { Steps, Loading, Empty } from 'react-vant'
import { Text } from '@tarojs/components'

const OrderExpress = () => {
  const router = useRouter()

  const { expressId } = router.params

  // 获取物流详情
  const [expressState, expressFetch] = useAsyncFn(async () => {
    if (!expressId) return
    const res = await queryExpressTrack(expressId)
    console.log('res', res)
    return res.data
  }, [])

  useEffect(() => {
    expressFetch()
  }, [expressFetch])

  // 格式化物流数据为 Steps 组件需要的格式
  const formatTrackingData = () => {
    if (!expressState.value?.data) return []

    return expressState.value.data.map((item) => ({
      title: item.context,
      description: item.ftime
    }))
  }

  // 渲染收货地址信息
  const renderAddressInfo = () => {
    const data = expressState.value
    if (!data) return null

    return (
      <div className="bg-white p-4 mb-2">
        <div className="flex items-center mb-2">
          <Text className="text-[30px] font-semibold">收货信息</Text>
        </div>
        <div className="space-y-1">
          <div className="text-gray-700">收货人：{data.receiver}</div>
          <div className="text-gray-700">联系电话：{data.receiverMobile}</div>
          <div className="text-gray-700">收货地址：******</div>
        </div>
      </div>
    )
  }

  // 渲染快递信息
  const renderExpressInfo = () => {
    const data = expressState.value
    if (!data) return null

    return (
      <div className="bg-white p-4 mb-2">
        <div className="flex items-center mb-2">
          <Text className="text-[30px] font-semibold">快递信息</Text>
        </div>
        <div className="space-y-1">
          <div className="text-gray-700">快递公司：{data.outsideExpressCompany}</div>
          <div className="text-gray-700">快递单号：{data.outsideExpressNo}</div>
        </div>
      </div>
    )
  }

  if (expressState.loading) {
    return (
      <div className="flex justify-center items-center h-screen">
        <Loading size="24px" />
      </div>
    )
  }

  if (expressState.error || !expressState.value) {
    return (
      <div className="flex justify-center items-center h-screen">
        <Empty description="暂无物流信息" />
      </div>
    )
  }

  const trackingSteps = formatTrackingData()

  return (
    <div className="min-h-screen bg-gray-50 text-[24px]">
      {/* 收货信息 */}
      {renderAddressInfo()}

      {/* 快递信息 */}
      {renderExpressInfo()}

      {/* 物流轨迹 */}
      <div className="bg-white p-4">
        <div className="flex items-center mb-4">
          <Text className="text-[30px] font-semibold">物流轨迹</Text>
        </div>

        {trackingSteps.length > 0 ? (
          <Steps direction="vertical" active={0}>
            {trackingSteps.map((step, index) => (
              <Steps.Item key={index}>
                <div className="mb-2">
                  <Text className="text-[24px] text-gray-900">{step.title}</Text>
                </div>
                <div>
                  <Text className="text-[24px] text-gray-500">{step.description}</Text>
                </div>
              </Steps.Item>
            ))}
          </Steps>
        ) : (
          <Empty description="暂无物流信息" />
        )}
      </div>
    </div>
  )
}

export default OrderExpress
