import Taro from '@tarojs/taro'

const SubmitBtn = ({ pay, totalPrice }: { pay: () => void; totalPrice: number }) => {
  return (
    <>
      <div className="h-[176px] fixed bottom-0 left-0 w-full submit_btn_shadow">
        <div className="flex justify-evenly h-[80px] mt-[20px]">
          <div
            onClick={() => {
              pay()
            }}
            className="bg-[#000000] w-[700px] h-[80px] rounded-[16px] font-medium text-[28px] text-white leading-[24px] text-center not-italic flex_center"
          >
            <div className="font-medium text-[28px] text-white leading-[24px] text-center not-italic mr-[20px]">立即支付</div>
            <div className="flex items-end">
              <div className="font-medium text-[36px] text-white leading-none text-center not-italic">¥</div>
              <div className="font-medium text-[48px] text-white leading-none text-center not-italic">{totalPrice}</div>
            </div>
          </div>
        </div>
      </div>
      <div className="h-[176px]"></div>
    </>
  )
}

export default SubmitBtn
