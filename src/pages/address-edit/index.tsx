import { useEffect, useState } from 'react'
import Taro, { useRouter } from '@tarojs/taro'
import { useAsyncFn } from 'react-use'
import { useAtomValue } from 'jotai'
import { Form, Input, Button, Cascader, Toast } from 'react-vant'
import {
  getProvinces,
  getCities,
  getCounties,
  getTowns,
  createAddress,
  editAddress,
  CreateAddressParams,
  EditAddressParams,
  addressDetail
} from '@/api/address'
import { userinfoState } from '@/store/global'

interface CascaderValue {
  value: string
  text: string
  children?: CascaderValue[]
}

const Index = () => {
  const router = useRouter()
  const userinfo = useAtomValue(userinfoState)
  const { addressId } = router.params
  const isEdit = !!addressId

  // 表单数据
  const [formData, setFormData] = useState({
    province: '',
    provinceCode: '',
    city: '',
    cityCode: '',
    area: '',
    areaCode: '',
    street: '',
    streetCode: ''
  })

  // 获取地址详情
  const [, addressDetailFetch] = useAsyncFn(async () => {
    const res = await addressDetail(Number(addressId))
    console.log('res', res)

    if (res) {
      const addressData = res.data
      setFormData({
        province: addressData.province,
        provinceCode: addressData.provinceCode,
        city: addressData.city,
        cityCode: addressData.cityCode,
        area: addressData.area,
        areaCode: addressData.areaCode,
        street: addressData.street,
        streetCode: addressData.streetCode
      })
      form.setFieldsValue({
        contacts: addressData.contacts,
        phone: addressData.phone,
        detail: addressData.detail
      })

      // 自动构建级联选择器数据并回显
      await buildCascaderData(addressData)
    }
    return res.data
  }, [])

  useEffect(() => {
    if (addressId) {
      addressDetailFetch()
    }
  }, [addressId, addressDetailFetch])

  // 级联选择器的选项数据
  const [cascaderOptions, setCascaderOptions] = useState<CascaderValue[]>([])
  const [form] = Form.useForm()

  // 提交状态
  const [submitState, submitAction] = useAsyncFn(async () => {
    try {
      const values = await form.validateFields()

      const params = {
        contacts: values.contacts,
        phone: values.phone,
        detail: values.detail,
        province: formData.province,
        provinceCode: formData.provinceCode,
        city: formData.city,
        cityCode: formData.cityCode,
        area: formData.area,
        areaCode: formData.areaCode,
        street: formData.street,
        streetCode: formData.streetCode,
        userId: userinfo?.cid || 0
      }

      if (isEdit) {
        await editAddress({ ...params, addressId: Number(addressId) } as EditAddressParams)
        Toast.success('修改成功')
      } else {
        await createAddress(params as CreateAddressParams)
        Toast.success('添加成功')
      }

      setTimeout(() => {
        Taro.navigateBack()
      }, 1500)
    } catch (error: any) {
      if (error.errorInputs) {
        // 表单验证失败
        const firstError = error.errorInputs[0]
        Toast.fail(firstError.errors[0])
      } else {
        Toast.fail(isEdit ? '修改失败' : '添加失败')
      }
    }
  }, [form, formData, isEdit, addressId, userinfo])

  // 加载省份数据
  const loadProvinces = async (): Promise<CascaderValue[]> => {
    try {
      const res = await getProvinces()
      return res.data.map((item) => ({
        value: item.provinceCode,
        text: item.provinceName,
        children: []
      }))
    } catch (error) {
      console.error('获取省份数据失败:', error)
      return []
    }
  }

  // 加载城市数据
  const loadCities = async (provinceCode: string): Promise<CascaderValue[]> => {
    try {
      const res = await getCities(provinceCode)
      return res.data.map((item) => ({
        value: item.cityCode,
        text: item.cityName,
        children: []
      }))
    } catch (error) {
      console.error('获取城市数据失败:', error)
      return []
    }
  }

  // 加载区县数据
  const loadCounties = async (cityCode: string): Promise<CascaderValue[]> => {
    try {
      const res = await getCounties(cityCode)
      return res.data.map((item) => ({
        value: item.countyCode,
        text: item.countyName,
        children: []
      }))
    } catch (error) {
      console.error('获取区县数据失败:', error)
      return []
    }
  }

  // 加载街道数据
  const loadTowns = async (countyCode: string): Promise<CascaderValue[]> => {
    try {
      const res = await getTowns(countyCode)
      return res.data.map((item) => ({
        value: item.townCode,
        text: item.townName,
        isLeaf: true
      }))
    } catch (error) {
      console.error('获取街道数据失败:', error)
      return []
    }
  }

  // 构建级联选择器数据并回显选中值
  const buildCascaderData = async (addressData: any) => {
    try {
      // 加载省份数据
      const provinces = await loadProvinces()

      // 找到当前省份并加载城市数据
      const currentProvince = provinces.find((p) => p.value === addressData.provinceCode)
      if (currentProvince && addressData.cityCode) {
        const cities = await loadCities(addressData.provinceCode)
        currentProvince.children = cities

        // 找到当前城市并加载区县数据
        const currentCity = cities.find((c) => c.value === addressData.cityCode)
        if (currentCity && addressData.areaCode) {
          const counties = await loadCounties(addressData.cityCode)
          currentCity.children = counties

          // 找到当前区县并加载街道数据
          const currentCounty = counties.find((a) => a.value === addressData.areaCode)
          if (currentCounty && addressData.streetCode) {
            const towns = await loadTowns(addressData.areaCode)
            currentCounty.children = towns
          }
        }
      }

      setCascaderOptions(provinces)
    } catch (error) {
      console.error('构建级联数据失败:', error)
    }
  }

  const onChange = (val: string[], selectedRows: CascaderValue[]) => {
    if (val.length === 1) {
      // 选择省
      const selectedProvince = cascaderOptions.find((p) => p.value === val[0])
      if (selectedProvince && !selectedProvince.children?.length) {
        loadCities(val[0]).then((cities) => {
          selectedProvince.children = cities
          setCascaderOptions([...cascaderOptions])
        })
      }
      setFormData({
        ...formData,
        province: selectedRows[0]?.text || '',
        provinceCode: selectedRows[0]?.value || '',
        city: '',
        cityCode: '',
        area: '',
        areaCode: '',
        street: '',
        streetCode: ''
      })
    } else if (val.length === 2) {
      // 选择市
      const selectedCity = cascaderOptions.find((p) => p.value === val[0])?.children?.find((c) => c.value === val[1])
      if (selectedCity && !selectedCity.children?.length) {
        loadCounties(val[1]).then((counties) => {
          selectedCity.children = counties
          setCascaderOptions([...cascaderOptions])
        })
      }
      setFormData({
        ...formData,
        city: selectedRows[1]?.text || '',
        cityCode: selectedRows[1]?.value || '',
        area: '',
        areaCode: '',
        street: '',
        streetCode: ''
      })
    } else if (val.length === 3) {
      // 选择区
      const selectedCounty = cascaderOptions
        .find((p) => p.value === val[0])
        ?.children?.find((c) => c.value === val[1])
        ?.children?.find((a) => a.value === val[2])
      if (selectedCounty && !selectedCounty.children?.length) {
        loadTowns(val[2]).then((towns) => {
          selectedCounty.children = towns
          setCascaderOptions([...cascaderOptions])
        })
      }
      setFormData({
        ...formData,
        area: selectedRows[2]?.text || '',
        areaCode: selectedRows[2]?.value || '',
        street: '',
        streetCode: ''
      })
    } else if (val.length === 4) {
      setFormData({
        ...formData,
        street: selectedRows[3]?.text || '',
        streetCode: selectedRows[3]?.value || ''
      })
    }
  }

  // 初始化数据
  useEffect(() => {
    const initData = async () => {
      // 只在非编辑模式下加载省份数据，编辑模式下由 buildCascaderData 处理
      if (!isEdit) {
        const provinces = await loadProvinces()
        setCascaderOptions(provinces as CascaderValue[])
      }
    }

    initData()
  }, [isEdit])

  return (
    <div className="p-4 bg-gray-50 min-h-screen">
      <Form form={form}>
        <Form.Item name="contacts" label="联系人" rules={[{ required: true, message: '请输入联系人姓名' }]}>
          <Input placeholder="请输入联系人姓名" />
        </Form.Item>

        <Form.Item
          name="phone"
          label="联系电话"
          rules={[
            { required: true, message: '请输入联系电话' },
            { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码' }
          ]}
        >
          <Input placeholder="请输入联系电话" />
        </Form.Item>

        <Form.Item isLink name="address" label="所在地区" rules={[{ required: true, message: '请选择省市区街道' }]}>
          <Cascader popup={{ round: true }} title="请选择所在地区" options={cascaderOptions} onChange={onChange}>
            {(_, selectedRows, actions) => {
              // 优先使用 selectedRows，如果为空则使用 formData 中的数据
              const displayValue =
                selectedRows.length > 0
                  ? selectedRows.map((el) => el.text).join(',')
                  : [formData.province, formData.city, formData.area, formData.street].filter(Boolean).join(',')

              return <Input value={displayValue} readOnly placeholder="请选择所在地区" onClick={() => actions.open()} />
            }}
          </Cascader>
        </Form.Item>

        <Form.Item name="detail" label="详细地址" rules={[{ required: true, message: '请输入详细地址' }]}>
          <Input.TextArea rows={3} placeholder="请输入详细地址" />
        </Form.Item>
      </Form>

      <div className="mt-6 px-4">
        <Button type="primary" block loading={submitState.loading} onClick={submitAction}>
          {isEdit ? '保存修改' : '保存地址'}
        </Button>
      </div>
    </div>
  )
}

export default Index
