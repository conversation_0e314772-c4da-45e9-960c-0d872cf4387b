import { useEffect } from 'react'
import Taro, { useDidShow, useRouter } from '@tarojs/taro'
import { useAsyncFn } from 'react-use'
import { listAddresses, ListAddressesRes } from '@/api/address'
import useObjState from '@/hooks/useObjState'

const Index = () => {
  const router = useRouter()

  const { isCheck } = router.params

  // 地址列表状态
  const addressList = useObjState<ListAddressesRes['data']>([])

  // 获取地址列表
  const [getAddressListState, getAddressListFetch] = useAsyncFn(async () => {
    try {
      const res = await listAddresses()
      console.log('address list response:', res)
      addressList.set(res.data || [])
      return res.data
    } catch (error) {
      console.error('获取地址列表失败:', error)
      Taro.showToast({
        title: '获取地址列表失败',
        icon: 'none'
      })
      return []
    }
  }, [])

  // 页面加载时获取地址列表
  useEffect(() => {
    getAddressListFetch()
  }, [getAddressListFetch])

  // 编辑地址
  const handleEditAddress = (address: ListAddressesRes['data'][0]) => {
    // 导航到地址编辑页面，传递地址信息
    Taro.navigateTo({
      url: `/pages/address-edit/index?addressId=${address.id}`
    })
  }

  // 新增地址
  const handleAddAddress = () => {
    Taro.navigateTo({
      url: '/pages/address-edit/index'
    })
  }

  useDidShow(() => {
    // 每次页面显示时重新获取地址列表
    getAddressListFetch()
  })

  // 删除地址
  // const [deleteAddressState, deleteAddressFetch] = useAsyncFn(
  //   async (addressId: number) => {
  //     try {
  //       const res = await deleteAddress({ addressId })
  //       if (res.data) {
  //         Taro.showToast({
  //           title: '删除成功',
  //           icon: 'success'
  //         })
  //         // 重新获取地址列表
  //         getAddressListFetch()
  //       }
  //       return res.data
  //     } catch (error) {
  //       console.error('删除地址失败:', error)
  //       Taro.showToast({
  //         title: '删除失败，请重试',
  //         icon: 'none'
  //       })
  //     }
  //   },
  //   [getAddressListFetch]
  // )

  // const handleDeleteAddress = (addressId: number, addressInfo?: string) => {
  //   Taro.showModal({
  //     title: '确认删除',
  //     content: `确定要删除"${addressInfo || '此地址'}"吗？`,
  //     confirmColor: '#FF3B30',
  //     success: (res) => {
  //       if (res.confirm) {
  //         deleteAddressFetch(addressId)
  //       }
  //     }
  //   })
  // }

  // // 设置默认地址
  // const [setDefaultAddressState, setDefaultAddressFetch] = useAsyncFn(
  //   async (addressId: number) => {
  //     try {
  //       const res = await setDefaultAddress({ addressId })
  //       if (res.data) {
  //         Taro.showToast({
  //           title: '设置成功',
  //           icon: 'success'
  //         })
  //         // 重新获取地址列表
  //         getAddressListFetch()
  //       }
  //       return res.data
  //     } catch (error) {
  //       console.error('设置默认地址失败:', error)
  //       Taro.showToast({
  //         title: '设置失败，请重试',
  //         icon: 'none'
  //       })
  //     }
  //   },
  //   [getAddressListFetch]
  // )

  // const handleSetDefaultAddress = (addressId: number) => {
  //   setDefaultAddressFetch(addressId)
  // }

  const choose = (address) => {
    if (isCheck) {
      Taro.navigateBack({
        delta: 1,
        success: () => {
          Taro.setStorageSync('selectedAddress', address)
        }
      })
    }
  }

  return (
    <div className="min-h-screen bg-[#F5F5F5] pb-[120px]">
      {/* 地址列表 */}
      <div className="px-[30px] pt-[20px]">
        {getAddressListState.loading ? (
          <div className="flex justify-center items-center py-[100px]">
            <div className="text-[28px] text-[#999999]">加载中...</div>
          </div>
        ) : addressList.val.length > 0 ? (
          <div className="space-y-[20px]">
            {addressList.val.map((address, index) => (
              <div key={`${address.userId}-${address.ctime}-${index}`} className="bg-white rounded-[16px] p-[30px] shadow-sm">
                {/* 联系人信息 */}
                <div className="flex justify-between items-start mb-[20px]">
                  <div className="flex-1">
                    <div onClick={() => choose(address)} className="flex items-center mb-[10px]">
                      <span className="text-[32px] font-medium text-[#333333] mr-[20px]">{address.contacts || '未填写'}</span>
                      <span className="text-[28px] text-[#666666]">{address.phone || '未填写'}</span>
                    </div>

                    {/* 详细地址 */}
                    <div onClick={() => choose(address)} className="text-[26px] text-[#666666] leading-[36px]">
                      {`${address.province || ''}${address.city || ''}${address.area || ''}${address.street || ''}${address.detail || ''}`}
                    </div>
                  </div>

                  {/* 编辑按钮 */}
                  <div
                    onClick={() => handleEditAddress(address)}
                    className="ml-[20px] text-[24px] text-[#007AFF] border border-[#007AFF] rounded-[8px] px-[16px] py-[8px] active:bg-[#007AFF] active:text-white"
                  >
                    编辑
                  </div>
                </div>

                {/* 底部操作区 */}
                {/* <div className="flex justify-between items-center pt-[20px] border-t border-[#F0F0F0]">
                  <div
                    className={`flex items-center ${address.isDefault !== 1 && !setDefaultAddressState.loading ? 'cursor-pointer' : ''}`}
                    onClick={() => {
                      if (!address.isDefault && !setDefaultAddressState.loading) {
                        handleSetDefaultAddress(address.id || address.userId)
                      }
                    }}
                  >
                    <div className="w-[24px] h-[24px] rounded-full border-2 border-[#007AFF] mr-[10px] flex items-center justify-center">
                      {address.isDefault === 1 && <div className="w-[12px] h-[12px] rounded-full bg-[#007AFF]"></div>}
                    </div>
                    <span className="text-[24px] text-[#666666]">
                      {address.isDefault === 1 ? '默认地址' : setDefaultAddressState.loading ? '设置中...' : '设为默认'}
                    </span>
                  </div>

                  <div
                    onClick={() => handleDeleteAddress(address.id || address.userId, `${address.contacts || ''}的地址`)}
                    className={`text-[24px] text-[#FF3B30] px-[16px] py-[8px] rounded-[8px] ${
                      deleteAddressState.loading ? 'opacity-50' : 'active:bg-[#FF3B30] active:text-white'
                    }`}
                  >
                    删除
                  </div>
                </div> */}
              </div>
            ))}
          </div>
        ) : (
          /* 空状态 */
          <div className="flex flex-col items-center justify-center py-[100px]">
            <div className="w-[120px] h-[120px] bg-[#F0F0F0] rounded-full mb-[30px] flex items-center justify-center">
              <div className="text-[60px] text-[#CCCCCC]">📍</div>
            </div>
            <div className="text-[28px] text-[#999999] mb-[10px]">暂无收货地址</div>
            <div className="text-[24px] text-[#CCCCCC]">点击下方按钮添加新地址</div>
          </div>
        )}
      </div>

      {/* 底部添加地址按钮 */}
      <div className="fixed bottom-0 left-0 right-0 bg-white border-t border-[#F0F0F0] px-[30px] py-[20px]">
        <div
          onClick={handleAddAddress}
          className="w-full h-[80px] bg-[#007AFF] rounded-[16px] flex items-center justify-center active:bg-[#0056CC]"
        >
          <span className="text-[30px] font-medium text-white">+ 新增收货地址</span>
        </div>
      </div>
    </div>
  )
}

export default Index
