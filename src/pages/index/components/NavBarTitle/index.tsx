import Taro, { usePageScroll, useRouter } from '@tarojs/taro'
import { useEffect, useState } from 'react'
import { Image } from '@tarojs/components'
import Slice65Img from '@/assets/images/index/Slice-65.png'
import Slice66Img from '@/assets/images/index/Slice-66.png'
import useObjAtom from '@/hooks/useObjAtom'
import { drawerState } from '@/store'

const NavBarTitle = () => {
  const [height, setHeight] = useState(0)
  const drawer = useObjAtom(drawerState)

  useEffect(() => {
    if (Taro.getEnv() === 'WEAPP') {
      const rect = Taro.getMenuButtonBoundingClientRect()
      const { top } = rect || {}
      setHeight(top + 42)
    } else {
      setHeight(100)
    }
  }, [])

  return (
    <>
      <div className="bg-white flex items-end w-full fixed top-0 z-50" style={{ height: `${height}px` }}>
        <div className="flex items-center h-[108px] w-full">
          <div
            onClick={() => {
              drawer.set(true)
            }}
            className="w-[84px] h-full flex_center"
          >
            <Image className="anim_btn w-[36px] h-[36px]" src={Slice65Img} />
          </div>
          <div className="flex-1 flex_center">
            <div className="text-[32px] text-[#2A3447] mr-[4px]">ARTIN·爱定制</div>
            <Image className="anim_btn w-[40px] h-[40px]" src={Slice66Img} />
          </div>
          <div className="w-[84px]"></div>
        </div>
      </div>
      <div className="bg-white flex items-end w-full" style={{ height: `${height}px` }}></div>
    </>
  )
}

export default NavBarTitle
