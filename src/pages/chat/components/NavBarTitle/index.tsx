import Taro, { usePageScroll, useRouter } from '@tarojs/taro'
import { useEffect, useState } from 'react'
import { Image } from '@tarojs/components'
import retImg from '@/assets/images/detail/ret.png'
import Slice66Img from '@/assets/images/index/Slice-66.png'

const NavBarTitle = () => {
  const [height, setHeight] = useState(0)

  useEffect(() => {
    if (Taro.getEnv() === 'WEAPP') {
      const rect = Taro.getMenuButtonBoundingClientRect()
      const { top } = rect || {}
      setHeight(top + 42)
    } else {
      setHeight(56)
    }
  }, [])

  return (
    <>
      <div className="color_shadow bg-white flex items-end w-full fixed top-0 z-50" style={{ height: `${height}px` }}>
        <div className="flex items-center h-[108px] w-full">
          <div
            onClick={() => {
              Taro.navigateBack({ delta: 1 })
            }}
            className="w-[84px] h-full flex_center"
          >
            <Image className="anim_btn w-[36px] h-[36px]" src={retImg} />
          </div>
          <div className="flex-1 flex_center">
            <div className="text-[32px] text-[#2A3447] mr-[4px]">新对话</div>
            <Image className="anim_btn w-[40px] h-[40px]" src={Slice66Img} />
          </div>
          <div className="w-[84px]"></div>
        </div>
      </div>
      <div className="bg-white flex items-end w-full" style={{ height: `${height}px` }}></div>
    </>
  )
}

export default NavBarTitle
