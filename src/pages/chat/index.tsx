import BottomInput from '@/components/BottomInput'
import { messgaeListState } from '@/store/chat'
import useObj<PERSON>tom from '@/hooks/useObjAtom'
import { ChatMarkdown } from '@/components/ChatMarkdown'
import Taro from '@tarojs/taro'
import { useCallback, useEffect, useRef } from 'react'
import useObjState from '@/hooks/useObjState'
import NavBarTitle from './components/NavBarTitle'

const Index = () => {
  const messgaeList = useObjAtom(messgaeListState)
  const isWx = Taro.getEnv() === 'WEAPP'
  const chatListRef = useRef<HTMLDivElement>(null)
  const scrollToBottomRef = useRef<boolean>(false)
  const isScrollToBottom = useObjState(true) // 记录是否滚动到底部，用于判断是否需要滚动到底部

  // 滚动到底部
  const scrollToBottom = useCallback(() => {
    if (!scrollToBottomRef.current) {
      scrollToBottomRef.current = true
      setTimeout(() => {
        chatListRef.current?.scrollTo({
          top: chatListRef.current.scrollHeight,
          behavior: 'smooth'
        })
        isScrollToBottom.set(true)
        scrollToBottomRef.current = false
      }, 250)
    }
  }, [])

  // 滚动事件
  const handleScroll = useCallback((e: React.UIEvent<HTMLDivElement>) => {
    const { scrollTop, scrollHeight, clientHeight } = e.currentTarget
    const isAtBottom = scrollTop + clientHeight >= scrollHeight - 10
    isScrollToBottom.set(isAtBottom)
  }, [])

  return (
    <>
      <div className="w-full h-full bg-white overflow-hidden flex flex-col">
        <NavBarTitle />
        <div ref={chatListRef} onScroll={handleScroll} className="relative flex-1 overflow-auto pb-[172px] pt-[30px]">
          {messgaeList.val.map((message, index) => {
            if (!message.content) return null

            const isHuman = message.type === 'human'
            const containsImage = /!\[.*?\]\(.*?\)/.test(message.content) // 检测 Markdown 是否包含图片

            return (
              <div key={index} className={`flex rounded-[20px] ${isHuman ? 'justify-end mx-[20px]' : 'justify-start'} mb-2`}>
                <div
                  className={`px-[20px] py-[10px] text-[28px] rounded-lg w-auto ${
                    isHuman ? 'bg-[#000000] text-white' : 'text-black'
                  } ${isHuman && containsImage ? 'flex flex-col items-end' : ''}`} // 添加样式
                >
                  <ChatMarkdown content={message.content} position={isHuman ? 'right' : 'left'} />
                </div>
              </div>
            )
          })}
        </div>
        {isWx && <div className="h-[154px]"></div>}
      </div>
      <div className={`fixed z-50 left-0 w-full h-[112px] ${isWx ? 'bottom-[184px]' : 'bottom-[30px]'}`}>
        <BottomInput isScrollToBottom={isScrollToBottom.val} scrollToBottom={scrollToBottom} />
      </div>
    </>
  )
}

export default Index
