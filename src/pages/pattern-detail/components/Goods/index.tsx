import { Swiper, SwiperItem, View } from '@tarojs/components'
import { useState } from 'react'
import demoImg from '@/assets/images/inspiration/demo.png'
import zhengImg from '@/assets/images/detail/zheng.png'
import { ReadyTemplateSaveRes } from '@/api/detail'

// 辅助函数：根据颜色名称返回对应的十六进制颜色代码
function getColorHex(color: string) {
  const colorMap: Record<string, string> = {
    白色: '#ffffff',
    黑色: '#000000',
    紫色: '#800080',
    绿色: '#008000'
  }
  return colorMap[color] || '#ffffff'
}

const Goods = ({ id, data }: { id: string; data: ReadyTemplateSaveRes['data'] }) => {
  const imageUrl = data.imageUrl.split('，')

  return (
    <>
      <div className="mt-[20px] mb-[42px] mx-auto w-[690px] h-[918px] overflow-hidden shadow-[0px_4_20px_0px_rgba(0,0,0,0.06)] rounded-[24px] border-2 border-solid border-[rgba(48,51,53,0.05)]">
        <img className="w-[690px] h-[918px] object-contain" src={imageUrl[0]} alt="" />
      </div>

      <div className="px-[42px] mb-[52px]">
        <div className="">
          <div className="font-normal text-[26px] text-[#202020] leading-[44px] text-left not-italic mb-[20px]">{data.des}</div>
          <div className="flex mb-[36px]">
            {data.tagList.map((item) => (
              <div
                key={item.id}
                className="w-[88px] h-[48px] bg-[#00000010] mr-[12px] flex_center rounded-[8px] font-normal text-[24px] text-[#00000098] leading-[34px] text-left not-italic"
              >
                {item.tagName}
              </div>
            ))}
          </div>
          <div className="flex justify-between items-center">
            <div className="font-normal text-[24px] text-[#202020] leading-[44px] text-left not-italic">设计者：{data.designUser}</div>
            <img className="w-[76px] h-[76px] rounded-full" src={data.designUserHead} alt="" />
          </div>
        </div>
      </div>
    </>
  )
}

export default Goods
