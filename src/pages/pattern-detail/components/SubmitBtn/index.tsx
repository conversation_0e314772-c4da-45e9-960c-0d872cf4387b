import designImg from '@/assets/images/detail/design1.png'
import share1Img from '@/assets/images/detail/share1.png'
import collectImg from '@/assets/images/inspiration/collect.png'
import collect1Img from '@/assets/images/inspiration/collect1.png'
import Taro from '@tarojs/taro'
import { ReadyTemplateSaveRes } from '@/api/detail'
import { collectImage } from '@/api/inspiration'
import { useEffect, useState } from 'react'
import { useAsyncFn } from 'react-use'
import { productListState } from '@/store/inspiration'
import useObjAtom from '@/hooks/useObjAtom'

const SubmitBtn = ({ data }: { data: ReadyTemplateSaveRes['data'] }) => {
  // data.customNum是人数，转换为w人，保留2位小数
  const customNum = (data.customNum / 10000).toFixed(1)
  const [collect, setCollect] = useState(data.collect)
  const productList = useObjAtom(productListState)

  useEffect(() => {
    setCollect(data.collect)
  }, [data.collect])

  const [collectState, collectFetch] = useAsyncFn(async () => {
    const res = await collectImage(data.id, collect ? '0' : '1')
    console.log('response', res)
    if (res.code === 200) {
      Taro.showToast({
        title: data.collect ? '取消收藏成功' : '收藏成功',
        icon: 'none'
      })
      setCollect(collect ? 0 : 1)
      productList.set((prev) => {
        return prev.map((i) => {
          if (i.id === data.id) {
            return {
              ...i,
              collect: i.collect ? 0 : 1
            }
          }
          return i
        })
      })
    }
    return res
  }, [collect, data])

  const share = () => {
    Taro.showToast({
      title: '分享成功',
      icon: 'none'
    })
  }

  return (
    <>
      <div className="h-[176px] fixed bottom-0 left-0 w-full submit_btn_shadow">
        <div className="flex justify-evenly h-[80px] mt-[20px]">
          <div onClick={collectFetch} className="flex_center flex-col w-[52px]">
            <img className="w-[52px] h-[52px]" src={collect ? collect1Img : collectImg} alt="" />
            <div className="font-normal text-[18px] text-[#202020] leading-[26px] text-left not-italic">收藏</div>
          </div>

          <div onClick={share} className="flex_center flex-col w-[52px]">
            <img className="w-[52px] h-[52px]" src={share1Img} alt="" />
            <div className="font-normal text-[18px] text-[#202020] leading-[26px] text-left not-italic">分享</div>
          </div>

          <div
            onClick={() => {
              Taro.navigateTo({
                url: `/pages/canvas/index?id=${data.id}`
              })
            }}
            className="bg-[#000000] w-[480px] h-[80px] rounded-[16px] font-medium text-[28px] text-white leading-[24px] text-center not-italic flex_center"
          >
            <img className="w-[42px] h-[42px]" src={designImg} alt="" />
            <div className="mx-[6px_4px] font-medium text-[28px] text-white leading-[28px] text-center not-italic">设计同款</div>
            <div className="mx-[6px_4px] font-medium text-[28px] text-white leading-[28px] text-center not-italic">{customNum}万人</div>
          </div>
        </div>
      </div>
      <div className="h-[176px]"></div>
    </>
  )
}

export default SubmitBtn
