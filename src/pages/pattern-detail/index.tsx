import use<PERSON><PERSON><PERSON><PERSON><PERSON> from '@/hooks/useObjAtom'
import { readyTemplateSave } from '@/api/detail'
import { useRouter } from '@tarojs/taro'
import { useEffect } from 'react'
import { useAsyncFn } from 'react-use'
import NavBarTitle from './components/NavBarTitle'
import Goods from './components/Goods'
import SubmitBtn from './components/SubmitBtn'

const Index = () => {
  const router = useRouter()

  const [readyTemplateSaveState, readyTemplateSaveFetch] = useAsyncFn(async () => {
    console.log(router.params.id)
    if (!router.params.id) return
    const res = await readyTemplateSave(router.params.id)
    console.log('response', res)
    return res.data
  }, [])

  useEffect(() => {
    readyTemplateSaveFetch()
  }, [])

  return (
    <>
      <div className="w-full h-screen overflow-hidden flex flex-col bg-[#F8F8F8]">
        <NavBarTitle />
        <div className="relative flex-1 overflow-auto bg-[#F8F8F8]">
          {router.params.id && readyTemplateSaveState.value && <Goods id={router.params.id} data={readyTemplateSaveState.value} />}
        </div>
        {router.params.id && readyTemplateSaveState.value && <SubmitBtn data={readyTemplateSaveState.value} />}
      </div>
    </>
  )
}

export default Index
