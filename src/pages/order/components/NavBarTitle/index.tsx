import useObj<PERSON><PERSON> from '@/hooks/useObjAtom'
import { activeTabState, inspirationTabsState } from '@/store/inspiration'
import Taro, { usePageScroll, useRouter } from '@tarojs/taro'
import { useEffect, useState } from 'react'

const NavBarTitle = () => {
  const [height, setHeight] = useState(0)
  const activeTab = useObjAtom(activeTabState)
  const inspirationTabs = useObjAtom(inspirationTabsState)

  useEffect(() => {
    if (Taro.getEnv() === 'WEAPP') {
      const rect = Taro.getMenuButtonBoundingClientRect()
      const { top } = rect || {}
      setHeight(top + 42)
    } else {
      setHeight(100)
    }
  }, [])

  return (
    <>
      <div className="bg-[#F8F8F8] flex items-end w-full fixed top-0 z-50" style={{ height: `${height}px` }}>
        <div className="flex items-center h-[108px] w-full">
          <div className="w-[84px] flex_center"></div>
          <div className="flex-1 flex_center gap-[52px]">
            {inspirationTabs.val.map((item) => {
              return (
                <div
                  key={item.id}
                  className={`font-medium text-[32px] text-black leading-[44px] not-italic relative ${
                    activeTab.val === item.id
                      ? "opacity-100 after:content-[''] after:absolute after:w-[24px] after:rounded-[3px] after:h-[4px] after:bg-black after:bottom-[-6px] after:left-[20px]"
                      : 'opacity-50'
                  }`}
                  onClick={() => {
                    activeTab.set(item.id)
                  }}
                >
                  {item.name}
                </div>
              )
            })}
          </div>
          <div className="w-[84px]"></div>
        </div>
      </div>
      <div className="bg-white flex items-end w-full" style={{ height: `${height}px` }}></div>
    </>
  )
}

export default NavBarTitle
