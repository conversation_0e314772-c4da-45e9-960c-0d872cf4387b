import { atom } from 'jotai'
import demoImg from '@/assets/images/inspiration/demo.png'

interface Recommend {
  id: number
  img: string
  name: string
  price: number
}

export const recommendListState = atom<Recommend[]>([
  {
    id: 1,
    img: demoImg,
    name: '圆领T恤五分袖',
    price: 99
  },
  {
    id: 2,
    img: demoImg,
    name: '圆领T恤五分袖',
    price: 99
  },
  {
    id: 3,
    img: demoImg,
    name: '圆领T恤五分袖',
    price: 99
  },
  {
    id: 4,
    img: demoImg,
    name: '圆领T恤五分袖',
    price: 99
  },
  {
    id: 5,
    img: demoImg,
    name: '圆领T恤五分袖',
    price: 99
  },
  {
    id: 6,
    img: demoImg,
    name: '圆领T恤五分袖',
    price: 99
  },
  {
    id: 7,
    img: demoImg,
    name: '圆领T恤五分袖',
    price: 99
  },
  {
    id: 8,
    img: demoImg,
    name: '圆领T恤五分袖',
    price: 99
  }
])
