import { atom } from 'jotai'
import demoImg from '@/assets/images/inspiration/demo.png'
import { InspirationTab, ReadyTemplateListRes } from '@/api/inspiration'

export const inspirationTabsState = atom<InspirationTab[]>([])
export const activeTabState = atom<number>()
export const activeSubTagsState = atom((get) => {
  const inspirationTabs = get(inspirationTabsState)
  const activeTab = get(activeTabState)
  return inspirationTabs.find((item) => item.id === activeTab)?.subTags || []
})

export const productListState = atom<ReadyTemplateListRes['data']['list']>([])

export const activeProductGroupIdState = atom<number>()

interface Pattern {
  id: number
  img: string
  name: string
  collection: boolean
  use: number
}
export const patternListState = atom<Pattern[]>([
  {
    id: 1,
    img: demoImg,
    name: '设计灵感：简介文案全部展示，简介文案全部,设计灵感：简介文案全部展示，简介文案全部',
    collection: false,
    use: 25062
  },
  {
    id: 2,
    img: demoImg,
    name: '设计灵感：简介文案全部展示，简介文案全部,设计灵感：简介文案全部展示，简介文案全部',
    collection: false,
    use: 25062
  },
  {
    id: 3,
    img: demoImg,
    name: '设计灵感：简介文案全部展示，简介文案全部,设计灵感：简介文案全部展示，简介文案全部',
    collection: false,
    use: 25062
  },
  {
    id: 4,
    img: demoImg,
    name: '设计灵感：简介文案全部展示，简介文案全部,设计灵感：简介文案全部展示，简介文案全部',
    collection: false,
    use: 25062
  },
  {
    id: 5,
    img: demoImg,
    name: '设计灵感：简介文案全部展示，简介文案全部,设计灵感：简介文案全部展示，简介文案全部',
    collection: false,
    use: 25062
  },
  {
    id: 6,
    img: demoImg,
    name: '设计灵感：简介文案全部展示，简介文案全部,设计灵感：简介文案全部展示，简介文案全部',
    collection: false,
    use: 25062
  },
  {
    id: 7,
    img: demoImg,
    name: '设计灵感：简介文案全部展示，简介文案全部,设计灵感：简介文案全部展示，简介文案全部',
    collection: false,
    use: 25062
  },
  {
    id: 8,
    img: demoImg,
    name: '设计灵感：简介文案全部展示，简介文案全部,设计灵感：简介文案全部展示，简介文案全部',
    collection: false,
    use: 25062
  }
])
