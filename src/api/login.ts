import { api1Request } from '@/utils/request'

// 响应接口
export interface wxLoginRes {
  code: number
  msg: string
  data: {
    cid: string
    nickName: string
    openId: string
    role: string
    token: string
  }
}

/**
 * 登录-获取小程序OpenId
 * @param {string} jsCode jsCode
 * @returns
 */
export function wxLogin(jsCode: string): Promise<wxLoginRes> {
  return api1Request.post({
    url: `/api/login/mini/app`,
    data: { jsCode },
    header: {
      'Content-Type': 'application/x-www-form-urlencoded'
    }
  })
}

// 响应接口
export interface BindPhoneRes {}

/**
 * openId绑定手机号
 * @param {string} code 临时code
 * @param {string} openId openId
 * @returns
 */
export function bindPhone(code: string, openId: string): Promise<BindPhoneRes> {
  return api1Request.post({
    url: `/api/login/mini/app/phone`,
    data: { code, openId }
  })
}
