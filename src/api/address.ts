import { api1Request } from '@/utils/request'

// 响应接口
export interface GetProvincesRes {
  data: {
    id: number
    provinceCode: string
    provinceName: string
    countryCode: string
    level: number
    type: number
    simpleName: string
    postCode: string
    status: number
    del: number
    phoneCode: string
  }[]
}

/**
 * 省份列表
 * @returns
 */
export function getProvinces(): Promise<GetProvincesRes> {
  return api1Request.get({
    url: `/api/anon/location/provinces`
  })
}

// 响应接口
export interface GetCitiesRes {
  data: {
    id: number
    cityCode: string
    cityName: string
    provinceCode: string
    level: number
    type: number
    simpleName: string
    postCode: string
    status: number
    del: number
    phoneCode: string
  }[]
}

/**
 * 城市列表
 * @param {string} provinceCode 省份Code-provinceCode
 * @returns
 */
export function getCities(provinceCode: string): Promise<GetCitiesRes> {
  return api1Request.get({
    url: `/api/anon/location/cities?provinceCode=${provinceCode}`
  })
}

// 响应接口
export interface GetCountiesRes {
  data: {
    id: number
    countyCode: string
    countyName: string
    cityCode: string
    level: number
    type: number
    simpleName: string
    postCode: string
    status: number
    del: number
    phoneCode: string
  }[]
}

/**
 * 区域列表
 * @param {string} cityCode 城市Code-cityCode
 * @returns
 */
export function getCounties(cityCode: string): Promise<GetCountiesRes> {
  return api1Request.get({
    url: `/api/anon/location/counties?cityCode=${cityCode}`
  })
}

// 响应接口
export interface GetTownsRes {
  data: {
    id: number
    townCode: string
    townName: string
    countyCode: string
    level: number
    type: number
    simpleName: string
    postCode: string
    status: number
    del: number
    phoneCode: string
  }[]
}

/**
 * 街道列表
 * @param {string} countyCode 区域Code-countyCode
 * @returns
 */
export function getTowns(countyCode: string): Promise<GetTownsRes> {
  return api1Request.get({
    url: `/api/anon/location/towns?countyCode=${countyCode}`
  })
}

// 参数接口
export interface EditAddressParams {
  /*创建时间 */
  ctime?: number
  /*创建时间 */
  utime?: number
  /*删除，0-正常，1-删除 */
  del?: number
  /*省 */
  provinceCode?: string
  /*省 */
  province?: string
  /*市 */
  cityCode?: string
  /*市 */
  city?: string
  /*区 */
  areaCode?: string
  /*区 */
  area?: string
  /*街道ID */
  streetCode?: string
  /*街道 */
  street?: string
  /*详情 */
  detail?: string
  /*用户ID */
  userId?: number
  /*联系人 */
  contacts?: string
  /*联系人-电话 */
  phone?: string
}

// 响应接口
export interface EditAddressRes {
  data: number
}

/**
 * 编辑地址
 * @param {object} params 用户地址
 * @param {number} params.ctime 创建时间
 * @param {number} params.utime 创建时间
 * @param {number} params.del 删除，0-正常，1-删除
 * @param {string} params.provinceCode 省
 * @param {string} params.province 省
 * @param {string} params.cityCode 市
 * @param {string} params.city 市
 * @param {string} params.areaCode 区
 * @param {string} params.area 区
 * @param {string} params.streetCode 街道ID
 * @param {string} params.street 街道
 * @param {string} params.detail 详情
 * @param {number} params.userId 用户ID
 * @param {string} params.contacts 联系人
 * @param {string} params.phone 联系人-电话
 * @returns
 */
export function editAddress(params: EditAddressParams): Promise<EditAddressRes> {
  return api1Request.post({
    url: `/api/anon/address/edit`,
    data: params
  })
}

// 参数接口
export interface CreateAddressParams {
  /*创建时间 */
  ctime?: number
  /*创建时间 */
  utime?: number
  /*删除，0-正常，1-删除 */
  del?: number
  /*省 */
  provinceCode?: string
  /*省 */
  province?: string
  /*市 */
  cityCode?: string
  /*市 */
  city?: string
  /*区 */
  areaCode?: string
  /*区 */
  area?: string
  /*街道ID */
  streetCode?: string
  /*街道 */
  street?: string
  /*详情 */
  detail?: string
  /*用户ID */
  userId?: number
  /*联系人 */
  contacts?: string
  /*联系人-电话 */
  phone?: string
}

// 响应接口
export interface CreateAddressRes {
  data: number
}

/**
 * 新增地址
 * @param {object} params 用户地址
 * @param {number} params.ctime 创建时间
 * @param {number} params.utime 创建时间
 * @param {number} params.del 删除，0-正常，1-删除
 * @param {string} params.provinceCode 省
 * @param {string} params.province 省
 * @param {string} params.cityCode 市
 * @param {string} params.city 市
 * @param {string} params.areaCode 区
 * @param {string} params.area 区
 * @param {string} params.streetCode 街道ID
 * @param {string} params.street 街道
 * @param {string} params.detail 详情
 * @param {number} params.userId 用户ID
 * @param {string} params.contacts 联系人
 * @param {string} params.phone 联系人-电话
 * @returns
 */
export function createAddress(params: CreateAddressParams): Promise<CreateAddressRes> {
  return api1Request.post({
    url: `/api/anon/address/add`,
    data: params
  })
}

// 响应接口
export interface UserLastRes {
  data: {
    ctime: number // 创建时间
    utime: number // 更新时间
    del: number // 删除，0-正常，1-删除
    provinceCode: string // 省
    province: string // 省
    cityCode: string // 市
    city: string // 市
    areaCode: string // 区
    area: string // 区
    streetCode: string // 街道ID
    street: string // 街道
    detail: string // 详情
    userId: number // 用户ID
    contacts: string // 联系人
    phone: string // 联系人-电话
  }
}

/**
 * 用户最新的一个地址
 * @returns
 */
export function userLast(): Promise<UserLastRes> {
  return api1Request.get({
    url: `/api/anon/address/user/last`
  })
}

// 响应接口
export interface ListAddressesRes {
  data: {
    id?: number // 地址ID
    ctime: number // 创建时间
    utime: number // 更新时间
    del: number // 删除，0-正常，1-删除
    provinceCode: string // 省
    province: string // 省
    cityCode: string // 市
    city: string // 市
    areaCode: string // 区
    area: string // 区
    streetCode: string // 街道ID
    street: string // 街道
    detail: string // 详情
    userId: number // 用户ID
    contacts: string // 联系人
    phone: string // 联系人-电话
    isDefault?: number // 是否默认地址，1-默认，0-非默认
  }[]
}

/**
 * 地址列表
 * @returns
 */
export function listAddresses(): Promise<ListAddressesRes> {
  return api1Request.get({
    url: `/api/anon/address/list`
  })
}

// 响应接口
export interface DetailRes {
  data: {
    id?: number // 地址ID
    ctime: number // 创建时间
    utime: number // 更新时间
    del: number // 删除，0-正常，1-删除
    provinceCode: string // 省
    province: string // 省
    cityCode: string // 市
    city: string // 市
    areaCode: string // 区
    area: string // 区
    streetCode: string // 街道ID
    street: string // 街道
    detail: string // 详情
    userId: number // 用户ID
    contacts: string // 联系人
    phone: string // 联系人-电话
    isDefault?: number // 是否默认地址，1-默认，0-非默认
  }
}

/**
 * 地址详情
 * @param {string} id
 * @returns
 */
export function addressDetail(id: number): Promise<DetailRes> {
  return api1Request.get({
    url: `/api/anon/address/detail?id=${id}`
  })
}
