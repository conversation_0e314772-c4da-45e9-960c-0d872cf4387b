import { Collect } from '@/store/my'
import { api1Request } from '@/utils/request'

// 响应接口
export interface CollectImageListRes {
  data: Collect[]
}

/**
 * 收藏图片列表
 * @param {string} lastId 分页最后一条数据的id，第一次可不传
 * @param {string} size 分页数量，默认20
 * @returns
 */
export function collectImageList({ lastId = '', size = '20' }): Promise<CollectImageListRes> {
  return api1Request.get({
    url: `/api/index/anon/collect/image/list?lastId=${lastId}&size=${size}`
  })
}

// 响应接口
export interface InfoEditRes {}

/**
 * 编辑用户信息
 * @param {string} head 头像
 * @param {string} nickname 昵称
 * @returns
 */
export function infoEdit(head: string, nickname: string): Promise<InfoEditRes> {
  return api1Request.post({
    url: `/api/anon/user/info/edit`,
    data: { head, nickname }
  })
}
